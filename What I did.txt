I ran npm install -g @expo/cli@latest   
and nvm use 22.16.0
 then npx create-expo-app@latest mobile-app --template blank-typescript
cd mobile-app

- I did now this npm install @react-navigation/native @react-navigation/stack
npx expo install react-native-screens react-native-safe-area-context
npm install @react-navigation/native-stack

-cmd wsl --install  user is huwaei password is : huwaeiazeroual1

- did this now  npx @nestjs/cli new . --package-manager npm --skip-git

- docker compose -build "maybe not correct"
-npm install prisma @prisma/client
npx prisma init
- ON wsl i did cd then curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
- Then sudo apt-get install -y nodejs build-essential

- i have done these npm install ioredis @nestjs-modules/ioredis @aws-sdk/client-s3

on wsl ubuntu terminal inside the ref-api


----
I ran npm install -g @expo/cli@latest   
and nvm use 22.16.0
 then npx create-expo-app@latest mobile-app --template blank-typescript
cd mobile-app

- I did now this on cusrosr npm install @react-navigation/native @react-navigation/stack
npx expo install react-native-screens react-native-safe-area-context
npm install @react-navigation/native-stack

-cmd wsl --install  user is huwaei password is : huwaeiazeroual1

- did this now  npx @nestjs/cli new . --package-manager npm --skip-git

- docker compose -build "maybe not correct"
-npm install prisma @prisma/client
npx prisma init
- ON wsl i did cd then curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
- Then sudo apt-get install -y nodejs build-essential

- I had a chat with chatgpt then i told augment  this chat on refGPT.txt 

- I changed the .env to use docker host not localhost 
-Then I did these [# 1. Navigate to ref-api directory
cd /mnt/c/Users/<USER>/referee-management-platform/mobile-app/ref-api

# 2. Regenerate Prisma client with new binary targets
npx prisma generate

# 3. Go back to project root
cd /mnt/c/Users/<USER>/referee-management-platform/mobile-app

# 4. Rebuild and restart only the API container
docker compose up -d --build api

# 5. Check that API is running properly this hows if all running good
docker compose ps

# 6. Watch the API logs to ensure no errors
docker compose logs -f api]


--------------------------------------
I asked about websocket when wanted to do firebase and said no so we did this 
# In ref-api directory
cd /mnt/c/Users/<USER>/referee-management-platform/mobile-app/ref-api

# Install WebSocket dependencies
npm install @nestjs/websockets @nestjs/platform-socket.io socket.io

# Install types for development
npm install -D @types/socket.io


then graph ql 

cd /mnt/c/Users/<USER>/referee-management-platform/mobile-app/ref-api

# Install GraphQL dependencies
npm install @nestjs/graphql @nestjs/apollo graphql apollo-server-express

# Install types
npm install -D @types/graphql


then says to start containers first then do firebase 
cd /mnt/c/Users/<USER>/referee-management-platform/mobile-app

# Start all containers
docker compose up -d

# Check they're running
docker ps


then 

cd /c/Users/<USER>/referee-management-platform/mobile-app/ref-app

# Install Firebase JS SDK
npx expo install firebase

# Generate metro config
npx expo customize metro.config.js
-------------------------------------

Now I did 
npm uninstall @react-native-google-signin/google-signin
npm uninstall @types/react-native
then
npx expo install expo-web-browser expo-auth-session

-----

-Now auth isn't working with expo go. So i did native build and I did 
npm install @react-native-google-signin/google-signin
then
npx expo uninstall expo-web-browser expo-auth-session


