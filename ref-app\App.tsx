import React, { useEffect } from 'react';
import { NavigationContainer, DefaultTheme, DarkTheme } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Settings } from 'react-native-fbsdk-next';

// Import Firebase config to ensure initialization
import './firebaseConfig';
import { AuthProvider } from './contexts/AuthContext';
import AuthNavigator from './navigation/AuthNavigator';
import { ThemeProvider, useTheme } from './contexts/ThemeContext'; // Import useTheme
import { lightTheme, darkTheme } from './themes/colors'; // <-- IMPORTANT: Adjust this path to where your colors.ts is located

// This is the component that will use the theme context
const ThemedApp = () => {
  const { isDarkMode } = useTheme();

  // Create a custom navigation theme that matches your light theme
  const MyLightTheme = {
    ...DefaultTheme,
    colors: {
      ...DefaultTheme.colors,
      primary: lightTheme.primary,
      background: lightTheme.background, // This is the key fix
      card: lightTheme.card,
      text: lightTheme.text,
      border: lightTheme.border,
    },
  };

  // Create a custom navigation theme that matches your dark theme
  const MyDarkTheme = {
    ...DarkTheme,
    colors: {
      ...DarkTheme.colors,
      primary: darkTheme.primary,
      background: darkTheme.background, // This is the key fix
      card: darkTheme.card,
      text: darkTheme.text,
      border: darkTheme.border,
    },
  };

  useEffect(() => {
    // Initialize Facebook SDK
    Settings.setAppID('651727944558014'); // Replace with your actual Facebook App ID
    Settings.initializeSDK();
  }, []);

  return (
    <SafeAreaProvider>
      <AuthProvider>
        {/* Pass the correct theme to the NavigationContainer */}
        <NavigationContainer theme={isDarkMode ? MyDarkTheme : MyLightTheme}>
          <AuthNavigator />
        </NavigationContainer>
      </AuthProvider>
    </SafeAreaProvider>
  );
};


// The main App component now wraps everything in the ThemeProvider
export default function App() {
  return (
    <ThemeProvider>
      <ThemedApp />
    </ThemeProvider>
  );
}