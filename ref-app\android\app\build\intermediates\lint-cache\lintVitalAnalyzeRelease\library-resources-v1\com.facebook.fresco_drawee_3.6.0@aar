http://schemas.android.com/apk/res-auto;com.facebook.fresco:drawee:3.6.0;$GRADLE_USER_HOME/caches/8.13/transforms/7133ea62f60e2d183b5e312dbc30a8d5/transformed/drawee-3.6.0/res/values/values.xml,+attr:roundingBorderColor,0,V400830ea9,3f00830ee4,;color|reference:;roundingBorderColor,0,V400d6147a,2600d6149c,;:;failureImageScaleType,0,V400320549,b003d070c,;enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,;failureImageScaleType,0,V400a7114e,2800a71172,;:;roundWithOverlayColor,0,V4007f0e2d,37007f0e60,;color:;roundWithOverlayColor,0,V400d21420,2800d21444,;:;roundBottomRight,0,V400730cdc,3400730d0c,;boolean:;roundBottomRight,0,V400c61335,2300c61354,;:;progressBarImageScaleType,0,V400420753,b004d091a,;enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,;progressBarImageScaleType,0,V400ad11a7,2c00ad11cf,;:;backgroundImage,0,V400600b39,3500600b6a,;reference:;backgroundImage,0,V400b31211,2200b3122f,;:;roundAsCircle,0,V4006b0bf9,31006b0c26,;boolean:;roundAsCircle,0,V400be1298,2000be12b4,;:;roundTopRight,0,V400710ca5,3100710cd2,;boolean:;roundTopRight,0,V400c4130f,2000c4132b,;:;viewAspectRatio,0,V4000b00c3,31000b00f0,;float:;viewAspectRatio,0,V40096104e,220096106c,;:;fadeDuration,0,V40008008c,30000800b8,;integer:;fadeDuration,0,V400931028,1f00931043,;:;actualImageResource,0,V4008d0fcf,39008d1004,;reference:;progressBarImage,0,V400400717,3600400749,;reference:;progressBarImage,0,V400ab117e,2300ab119d,;:;progressBarAutoRotateInterval,0,V4004f0924,41004f0961,;integer:;progressBarAutoRotateInterval,0,V400b011da,3000b01206,;:;roundBottomStart,0,V4007b0dbb,34007b0deb,;boolean:;roundBottomStart,0,V400ce13d0,2300ce13ef,;:;actualImageScaleType,0,V40052096c,b005d0b2e,;enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,;roundBottomEnd,0,V4007d0df5,32007d0e23,;boolean:;roundBottomEnd,0,V400d013f9,2100d01416,;:;pressedStateOverlayImage,0,V400660bae,3e00660be8,;reference:;pressedStateOverlayImage,0,V400b91260,2b00b91287,;:;placeholderImageScaleType,0,V40012013d,b001d0304,;enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,;placeholderImageScaleType,0,V4009d10a6,2c009d10ce,;:;failureImage,0,V400300511,320030053f,;reference:;failureImage,0,V400a51129,1f00a51144,;:;roundingBorderPadding,0,V400850eee,3b00850f25,;dimension:;roundingBorderPadding,0,V400d814a6,2800d814ca,;:;retryImage,0,V40020030f,300020033b,;reference:;retryImage,0,V400a010d9,1d00a010f2,;:;roundBottomLeft,0,V400750d16,3300750d45,;boolean:;roundBottomLeft,0,V400c8135e,2200c8137c,;:;actualImageUri,0,V4008b0f98,31008b0fc5,;string:;roundTopStart,0,V400770d4f,3100770d7c,;boolean:;roundTopStart,0,V400ca1386,2000ca13a2,;:;retryImageScaleType,0,V400220345,b002d0506,;enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,;retryImageScaleType,0,V400a210fc,2600a2111e,;:;roundTopLeft,0,V4006f0c6f,30006f0c9b,;boolean:;roundTopLeft,0,V400c212ea,1f00c21305,;:;overlayImage,0,V400630b75,3200630ba3,;reference:;overlayImage,0,V400b6123a,1f00b61255,;:;roundTopEnd,0,V400790d86,2f00790db1,;boolean:;roundTopEnd,0,V400cc13ac,1e00cc13c6,;:;roundingBorderWidth,0,V400810e6a,3900810e9f,;dimension:;roundingBorderWidth,0,V400d4144e,2600d41470,;:;roundedCornerRadius,0,V4006d0c30,39006d0c65,;dimension:;roundedCornerRadius,0,V400c012be,2600c012e0,;:;placeholderImage,0,V400100101,3600100133,;reference:;placeholderImage,0,V4009b107d,23009b109c,;:;+styleable:SimpleDraweeView,0,V400880f42,1600da14e2,;-actualImageUri:string:-actualImageResource:reference:-fadeDuration::-viewAspectRatio::-placeholderImage::-placeholderImageScaleType::-retryImage::-retryImageScaleType::-failureImage::-failureImageScaleType::-progressBarImage::-progressBarImageScaleType::-progressBarAutoRotateInterval::-backgroundImage::-overlayImage::-pressedStateOverlayImage::-roundAsCircle::-roundedCornerRadius::-roundTopLeft::-roundTopRight::-roundBottomRight::-roundBottomLeft::-roundTopStart::-roundTopEnd::-roundBottomStart::-roundBottomEnd::-roundWithOverlayColor::-roundingBorderWidth::-roundingBorderColor::-roundingBorderPadding::;GenericDraweeHierarchy,0,V400020037,1600870f3d,;-fadeDuration:integer:-viewAspectRatio:float:-placeholderImage:reference:-placeholderImageScaleType:enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,-retryImage:reference:-retryImageScaleType:enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,-failureImage:reference:-failureImageScaleType:enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,-progressBarImage:reference:-progressBarImageScaleType:enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,-progressBarAutoRotateInterval:integer:-actualImageScaleType:enum:none:-1,fitXY:0,fitStart:1,fitCenter:2,fitEnd:3,center:4,centerInside:5,centerCrop:6,focusCrop:7,fitBottomStart:8,-backgroundImage:reference:-overlayImage:reference:-pressedStateOverlayImage:reference:-roundAsCircle:boolean:-roundedCornerRadius:dimension:-roundTopLeft:boolean:-roundTopRight:boolean:-roundBottomRight:boolean:-roundBottomLeft:boolean:-roundTopStart:boolean:-roundTopEnd:boolean:-roundBottomStart:boolean:-roundBottomEnd:boolean:-roundWithOverlayColor:color:-roundingBorderWidth:dimension:-roundingBorderColor:color|reference:-roundingBorderPadding:dimension:;