import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons, MaterialCommunityIcons, Feather, Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct

// --- Main Manage Component ---
const Manage = () => {
  const navigation = useNavigation<StackNavigationProp<any>>();
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);

  // --- Item definitions are moved inside the component to access the theme ---
  const profileItems = [
    { icon: <MaterialIcons name="person-outline" size={22} color={theme.text} />, label: 'Account', nav: 'Account' },
    { icon: <MaterialIcons name="settings" size={22} color={theme.text} />, label: 'Settings', nav: 'Settings' },
    { icon: <MaterialCommunityIcons name="puzzle-outline" size={22} color={theme.text} />, label: 'Integrations', nav: 'Integrations' },
    {
      icon: <MaterialCommunityIcons name="medal-outline" size={22} color={theme.primary} />,
      label: 'Go Pro Now',
      labelStyle: styles.goProLabel,
      nav: 'GoPro'
    },
  ];

  const helpItems = [
    { icon: <MaterialIcons name="help-outline" size={22} color={theme.text} />, label: 'Support', nav: 'Support' },
    { icon: <MaterialCommunityIcons name="run-fast" size={22} color={theme.text} />, label: 'App Tutorial', nav: 'AppTutorial' },
    { icon: <MaterialCommunityIcons name="dumbbell" size={22} color={theme.text} />, label: 'Fitness Sync', nav: 'FitnessSync' },
    { icon: <MaterialCommunityIcons name="star-circle-outline" size={22} color={theme.text} />, label: 'Why Us', nav: 'WhyUs' },
    { icon: <Feather name="share-2" size={22} color={theme.text} />, label: 'Share this app', onPress: () => { /* Share functionality */ } },
    { icon: <MaterialIcons name="android" size={22} color={theme.text} />, label: 'Rate us on Google Play', labelStyle: styles.boldLabel, onPress: () => { /* Open Play Store */ } },
    { icon: <Ionicons name="logo-apple" size={22} color={theme.text} />, label: 'Rate us on Apple Store', labelStyle: styles.boldLabel, onPress: () => { /* Open App Store */ } },
  ];

  const renderItem = (item: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.row}
      onPress={() => {
        if (item.nav) {
          navigation.navigate(item.nav as never);
        } else if (item.onPress) {
          item.onPress();
        }
      }}
    >
      <View style={styles.rowLeft}>
        {item.icon}
        <Text style={[styles.rowLabel, item.labelStyle]}>{item.label}</Text>
      </View>
      <MaterialIcons name="chevron-right" size={22} color={theme.text} style={{ opacity: 0.5 }}/>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Header title="Manage" />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>

        <Text style={styles.sectionHeader}>Profile</Text>
        <View style={styles.sectionBox}>
          {profileItems.map((item, idx) => renderItem(item, idx))}
        </View>

        <Text style={styles.sectionHeader}>More Options</Text>
        <View style={styles.sectionBox}>
          {helpItems.map((item, idx) => renderItem(item, idx))}
        </View>
        <View style={styles.sectionBox}>
            <TouchableOpacity style={styles.logoutRow} activeOpacity={0.7}>
                <MaterialIcons name="power-settings-new" size={22} color="#d32f2f" />
                <Text style={styles.logoutLabel}>Logout</Text>
            </TouchableOpacity>
        </View>

      </ScrollView>
    </View>
  );
};

// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
    paddingTop: 16,
  },
  sectionHeader: {
    fontWeight: '700',
    fontSize: 15,
    color: theme.text,
    marginTop: 10,
    marginBottom: 12,
    marginLeft: 4,
  },
  sectionBox: {
    backgroundColor: theme.card,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 4,
    marginBottom: 18,
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.10,
    shadowRadius: 2.00,
    elevation: isDarkMode ? 0 : 2,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 14,
    backgroundColor: theme.card,
    borderWidth: 1,
    borderColor: theme.border1, // Use the main border color for consistency
    borderRadius: 10,
    marginBottom: 10,
  },
  rowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowLabel: {
    fontSize: 15,
    color: theme.text,
    marginLeft: 14,
    fontWeight: '500',
  },
  goProLabel: {
    color: theme.primary,
    fontWeight: 'bold',
  },
  boldLabel: {
    fontWeight: 'bold',
  },
  logoutRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingVertical: 14,
    paddingHorizontal: 14,
    backgroundColor: theme.card,
    borderWidth: 1,
    borderColor: theme.border,
    borderRadius: 10,
    marginBottom: 10,
  },
  logoutLabel: {
    fontSize: 15,
    color: '#d32f2f',
    marginLeft: 14,
    fontWeight: '600',
  },
});

export default Manage;