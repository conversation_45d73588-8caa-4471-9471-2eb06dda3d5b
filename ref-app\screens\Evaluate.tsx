import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions
} from 'react-native';
import Header from '../components/Header';
import Matches from './EvaluateSections/Matches';
import Sprints from './EvaluateSections/Sprints';
import Misconduct from './EvaluateSections/Misconduct';
import Positioning from './EvaluateSections/Positioning';
import Distance from './EvaluateSections/Distance';
import Health from './EvaluateSections/Health';
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct

const { width: SCREEN_WIDTH } = Dimensions.get('window');

type TabType = {
  key: string;
  label: string;
};

const TABS: TabType[] = [
  { key: 'matches', label: 'Matches' },
  { key: 'sprints', label: 'Sprints' },
  { key: 'misconduct', label: 'Misconduct' },
  { key: 'positioning', label: 'Positioning' },
  { key: 'distance', label: 'Distance' },
  { key: 'health', label: 'Health' },
];

const Evaluate = () => {
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);
  
  const [activeTab, setActiveTab] = useState<string>(TABS[0].key); // Default to sprints
  const scrollViewRef = useRef<ScrollView>(null);
  const [tabLayouts, setTabLayouts] = useState<Record<number, {x: number, width: number}>>({});

  const activeTabIndex = TABS.findIndex(tab => tab.key === activeTab);

  useEffect(() => {
    if (tabLayouts[activeTabIndex]) {
      const { x, width } = tabLayouts[activeTabIndex];
      const scrollToX = x - SCREEN_WIDTH / 2 + width / 2;
      scrollViewRef.current?.scrollTo({ x: scrollToX, animated: true });
    }
  }, [activeTabIndex, tabLayouts]);

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab.key);
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'matches':
        return <Matches />;
      case 'sprints':
        return <Sprints />;
      case 'misconduct':
        return <Misconduct />;
      case 'positioning':
        return <Positioning />;
      case 'distance':
        return <Distance />;
      case 'health':
        return <Health />;
      default:
        return (
          <View style={styles.centeredContent}>
            <Text style={styles.contentText}>Select a tab</Text>
          </View>
        );
    }
  };


  return (
    <View style={styles.container}>
      <Header title="Evaluate" showBackButton={false} />

      <View style={styles.tabsContainer}>
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsScrollContainer}
          bounces={false}
        >
          {TABS.map((tab, index) => (
            <TouchableOpacity
              key={tab.key}
              onLayout={(event) => {
                const { x, width } = event.nativeEvent.layout;
                setTabLayouts((prev) => ({ ...prev, [index]: { x, width } }));
              }}
              style={[
                styles.tab,
                activeTab === tab.key && styles.tabActive,
              ]}
              onPress={() => handleTabPress(tab)}
              activeOpacity={0.8}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.key && styles.tabTextActive,
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.contentContainer}>
        {renderContent()}
      </View>
    </View>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  tabsContainer: {
    backgroundColor: theme.background,
    paddingVertical: 8,
    paddingHorizontal: 6,
    marginBottom:0,
  },
  tabsScrollContainer: {
    paddingHorizontal: 0,
  },
  tab: {
    paddingHorizontal: 14,
    paddingVertical: 6,
    backgroundColor: theme.card,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabActive: {
    backgroundColor: theme.primary,
    borderColor: theme.primary,
  },
  tabText: {
    fontSize: 13,
    fontWeight: '600',
    textAlign: 'center',
    color: theme.text,
  },
  tabTextActive: {
    color: '#fff', // White text on primary color is standard
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  centeredContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 9,
  },
  contentText: {
    color: theme.text,
    opacity: 0.6,
    fontSize: 16,
  },
});

export default Evaluate;