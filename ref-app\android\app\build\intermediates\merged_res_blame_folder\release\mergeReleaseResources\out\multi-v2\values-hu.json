{"logs": [{"outputFile": "com.refrate.refereetracker.app-mergeReleaseResources-54:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\97ed3adf425b530d685cdd8470e3d9b5\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "39,40,41,42,43,44,45,182", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3549,3646,3748,3850,3951,4054,4161,16993", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3641,3743,3845,3946,4049,4156,4266,17089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d29e10314c2a4b4c7355c13794550e06\\transformed\\play-services-base-18.0.1\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7258,7369,7553,7691,7800,7968,8106,8228,8515,8685,8793,8978,9115,9287,9459,9530,9598", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "7364,7548,7686,7795,7963,8101,8223,8333,8680,8788,8973,9110,9282,9454,9525,9593,9681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6550f91160cccf4761958ba318c6d009\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,92,93,94,99,102,104,105,106,107,108,109,110,111,112,113,114,115,116,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,171,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3145,3226,3302,3379,3469,4271,4370,4490,9782,9845,9909,10399,10620,10752,10862,10924,10993,11051,11123,11184,11239,11342,11399,11459,11514,11595,11930,12013,12091,12187,12273,12361,12496,12579,12659,12799,12893,12975,13028,13079,13145,13221,13303,13374,13458,13535,13610,13689,13766,13871,13967,14044,14136,14233,14307,14392,14489,14541,14624,14691,14779,14866,14928,14992,15055,15121,15219,15325,15419,15526,15583,15638,16128,16213,16290", "endLines": "5,34,35,36,37,38,46,47,48,92,93,94,99,102,104,105,106,107,108,109,110,111,112,113,114,115,116,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,171,172,173", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "308,3221,3297,3374,3464,3544,4365,4485,4568,9840,9904,10003,10469,10674,10857,10919,10988,11046,11118,11179,11234,11337,11394,11454,11509,11590,11710,12008,12086,12182,12268,12356,12491,12574,12654,12794,12888,12970,13023,13074,13140,13216,13298,13369,13453,13530,13605,13684,13761,13866,13962,14039,14131,14228,14302,14387,14484,14536,14619,14686,14774,14861,14923,14987,15050,15116,15214,15320,15414,15521,15578,15633,15718,16208,16285,16358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f64dc84a821f94336e410d15a520d5cf\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,16044", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,16123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3cd7b7d97291c621d53ca066c44c69e\\transformed\\play-services-basement-18.2.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "8338", "endColumns": "176", "endOffsets": "8510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f4f1d68817d697725d9ad4b19dc40c62\\transformed\\facebook-login-18.0.3\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,353,506,617,701,789,865,954,1044,1159,1272,1369,1466,1572,1704,1786,1872,2066,2162,2268,2383,2499", "endColumns": "168,128,152,110,83,87,75,88,89,114,112,96,96,105,131,81,85,193,95,105,114,115,158", "endOffsets": "219,348,501,612,696,784,860,949,1039,1154,1267,1364,1461,1567,1699,1781,1867,2061,2157,2263,2378,2494,2653"}, "to": {"startLines": "49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4573,4742,4871,5024,5135,5219,5307,5383,5472,5562,5677,5790,5887,5984,6090,6222,6304,6390,6584,6680,6786,6901,7017", "endColumns": "168,128,152,110,83,87,75,88,89,114,112,96,96,105,131,81,85,193,95,105,114,115,158", "endOffsets": "4737,4866,5019,5130,5214,5302,5378,5467,5557,5672,5785,5882,5979,6085,6217,6299,6385,6579,6675,6781,6896,7012,7171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9a19d447f99dacc987e9045d6bd5cdca\\transformed\\browser-1.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "91,95,96,97", "startColumns": "4,4,4,4", "startOffsets": "9686,10008,10109,10224", "endColumns": "95,100,114,103", "endOffsets": "9777,10104,10219,10323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e7eb301ebab3935e1c8a7c29a974d3ae\\transformed\\react-android-0.79.4-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,215,286,353,432,505,572,645,720,803,892,963,1041,1120,1198,1283,1364,1440,1510,1579,1671,1746,1828,1899", "endColumns": "77,81,70,66,78,72,66,72,74,82,88,70,77,78,77,84,80,75,69,68,91,74,81,70,74", "endOffsets": "128,210,281,348,427,500,567,640,715,798,887,958,1036,1115,1193,1278,1359,1435,1505,1574,1666,1741,1823,1894,1969"}, "to": {"startLines": "33,72,98,100,101,103,117,118,119,166,167,168,169,174,175,176,177,178,179,180,181,183,184,185,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3067,7176,10328,10474,10541,10679,11715,11782,11855,15723,15806,15895,15966,16363,16442,16520,16605,16686,16762,16832,16901,17094,17169,17251,17322", "endColumns": "77,81,70,66,78,72,66,72,74,82,88,70,77,78,77,84,80,75,69,68,91,74,81,70,74", "endOffsets": "3140,7253,10394,10536,10615,10747,11777,11850,11925,15801,15890,15961,16039,16437,16515,16600,16681,16757,16827,16896,16988,17164,17246,17317,17392"}}]}]}