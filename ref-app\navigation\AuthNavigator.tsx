import React from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext'; // <-- Step 1: Import useTheme

// Import screens
import LoginScreen from '../screens/LoginScreen';
import ProfileSetup from '../screens/ProfileSetup';
import ConnectWatch from '../screens/ConnectWatch';
import Dashboard from '../screens/Dashboard';
import Evaluate from '../screens/Evaluate';
import Account from '../screens/Account';
import Settings from '../screens/Settings';
import Integrations from '../screens/Integrations';
import CreateMatchScreen from '../screens/CreateMatchScreen';
import FilterMatches from '../screens/FilterMatches';
import AppTutorialScreen from '../screens/AppTutorialScreen';
import GoProScreen from '../screens/GoProScreen';
import SupportScreen from '../screens/SupportScreen';
import FAQScreen from '../screens/FAQScreen';
import FeatureRequestsScreen from '../screens/FeatureRequestsScreen';
import WatchDebuggerScreen from '../screens/WatchDebuggerScreen';
import ContactUsScreen from '../screens/ContactUsScreen';
import TermsConditionsScreen from '../screens/TermsConditionsScreen';
import PrivacyPolicyScreen from '../screens/PrivacyPolicyScreen';
import FitnessSyncScreen from '../screens/FitnessSyncScreen';
import WhyUsScreen from '../screens/WhyUsScreen';
import MatchDetailScreen from '../screens/MatchDetailScreen';
import MatchResultsScreen from '../screens/MatchResultsScreen';
import EvaluateQuestions from '../screens/EvaluateQuestions';

const Stack = createStackNavigator();

const AuthNavigator: React.FC = () => {
  const { user, loading } = useAuth();
  const { theme } = useTheme(); // <-- Step 2: Get the theme object

  // Show loading screen while checking auth state
  if (loading) {
    // --- Themed Loading Screen ---
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color={theme.primary} />
      </View>
    );
  }

  return (
    <Stack.Navigator
      // --- Step 3: Apply themed screenOptions to the entire stack ---
      screenOptions={{
        headerShown: false,
        // This is the KEY FIX that applies the theme's background color
        // to all screen cards, preventing the white flash during transitions.
        cardStyle: { backgroundColor: theme.background },
      }}
    >
      {user ? (
        // User is signed in - show app screens
        <>
          <Stack.Screen name="Dashboard" component={Dashboard} />
          <Stack.Screen name="ProfileSetup" component={ProfileSetup} />
          <Stack.Screen name="ConnectWatch" component={ConnectWatch} />
          <Stack.Screen name="Account" component={Account} options={{ presentation: 'modal' }} />
          <Stack.Screen name="Settings" component={Settings} options={{ presentation: 'modal' }} />
          <Stack.Screen name="Integrations" component={Integrations} options={{ presentation: 'modal' }} />
          <Stack.Screen name="Evaluate" component={Evaluate} />
          <Stack.Screen 
            name="CreateMatch" 
            component={CreateMatchScreen} 
            options={{
              presentation: 'modal',
              // cardStyle is now inherited from screenOptions, no need to repeat unless different
            }} 
          />
          <Stack.Screen 
            name="FilterMatches" 
            component={FilterMatches} 
            options={{
              presentation: 'modal',
              // cardStyle is now inherited
            }} 
          />
          <Stack.Screen name="AppTutorial" component={AppTutorialScreen} options={{ presentation: 'modal' }} />
          <Stack.Screen name="GoPro" component={GoProScreen} options={{ presentation: 'modal' }} />
          <Stack.Screen name="EvaluateQuestions" component={EvaluateQuestions} />
          <Stack.Screen name="Support" component={SupportScreen} options={{ presentation: 'modal' }} />
          <Stack.Screen name="FAQ" component={FAQScreen} />
          <Stack.Screen name="FeatureRequests" component={FeatureRequestsScreen} />
          <Stack.Screen name="WatchDebugger" component={WatchDebuggerScreen} />
          <Stack.Screen name="ContactUs" component={ContactUsScreen} />
          <Stack.Screen name="TermsConditions" component={TermsConditionsScreen} />
          <Stack.Screen name="PrivacyPolicy" component={PrivacyPolicyScreen} />
          <Stack.Screen name="FitnessSync" component={FitnessSyncScreen} options={{ presentation: 'modal' }} />
          <Stack.Screen name="WhyUs" component={WhyUsScreen} options={{ presentation: 'modal' }} />
          <Stack.Screen name="MatchDetail" component={MatchDetailScreen} options={{ presentation: 'modal' }} />
          <Stack.Screen name="MatchResults" component={MatchResultsScreen} options={{ presentation: 'modal' }} />
        </>
      ) : (
        // User is not signed in - show auth screens
        // The cardStyle from screenOptions will apply here too, theming the login screen background
        <Stack.Screen name="Login" component={LoginScreen} />
      )}
    </Stack.Navigator>
  );
};

// Stylesheet remains simple as themeing is handled dynamically
const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AuthNavigator;