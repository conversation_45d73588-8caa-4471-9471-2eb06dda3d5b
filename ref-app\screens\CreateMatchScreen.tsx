import React, { useState, Dispatch, SetStateAction, useEffect, ReactNode } from 'react';
import {
  View, Text, ScrollView, TouchableOpacity, StyleSheet, Platform,
  TextInput, LayoutAnimation, UIManager, SafeAreaView, KeyboardTypeOptions,
  Modal, KeyboardAvoidingView, // Added KeyboardAvoidingView
} from 'react-native';
import Svg, { Path, Circle, Defs, ClipPath, G, Rect } from 'react-native-svg';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct

// --- Setup and Helper Definitions ---

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// Type Definitions
interface SvgIconProps { color?: string; size?: number; }
interface Official { id: number; role: string; name: string; }
interface KitColors { base: string; stripe: string; }
interface StripedTshirtProps { baseColor: string; stripeColor: string; size?: number; }

// SVG Components
const ChevronDownIcon: React.FC<SvgIconProps> = ({ color, size = 18 }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M6 9L12 15L18 9" stroke={color} strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/></Svg> );
const ChevronUpIcon: React.FC<SvgIconProps> = ({ color, size = 18 }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M18 15L12 9L6 15" stroke={color} strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/></Svg> );
const PlusIcon: React.FC<SvgIconProps> = ({ color, size = 16 }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M12 5V19M5 12H19" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/></Svg> );
const MinusIcon: React.FC<SvgIconProps> = ({ color, size = 16 }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M5 12H19" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/></Svg> );
const UserPlusIcon: React.FC<SvgIconProps> = ({ color, size = 22 }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><Circle cx="8.5" cy="7" r="4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><Path d="M20 8v6M23 11h-6" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></Svg> );
const CloseCircleIcon: React.FC<SvgIconProps> = ({ color, size = 20 }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="1.5"/><Path d="M15 9L9 15M9 9L15 15" stroke={color} strokeWidth="1.5" strokeLinecap="round"/></Svg> );
const StripedTshirt: React.FC<StripedTshirtProps> = ({ baseColor, stripeColor, size = 36 }) => (
  <Svg width={size} height={size} viewBox="-1 0 19 19">
    <Defs><ClipPath id="tshirtClip"><Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" /></ClipPath></Defs>
    <Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" fill={baseColor} />
    <G clipPath="url(#tshirtClip)">{[3.5, 6, 8.5, 11, 13.5].map(x => ( <Rect key={x} x={x} y="3" width="1.2" height="13" fill={stripeColor} /> ))}</G>
  </Svg>
);

// Constants
const colorPalette = ['#FFFFFF', '#000000', '#FF0000', '#0000FF', '#008000', '#FFFF00', '#FFA500', '#800080', '#FFC0CB', '#A52A2A', '#808080', '#00FFFF'];
let nextOfficialId = 0;
const generateId = () => { nextOfficialId += 1; return nextOfficialId; };
const CREATE_NEW_TEMPLATE = '__CREATE_NEW__';
const preSavedTemplates = ['League sin 16', 'Cup Final Rules', 'Youth League U12'];

// Prop Interfaces
interface FormFieldRowProps { label: string; value?: string | number | null; placeholder?: string; children?: React.ReactNode; showArrow?: boolean; onPress?: () => void; isEditable?: boolean; onChangeText?: (text: string) => void; keyboardType?: KeyboardTypeOptions; autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'; arrowIcon?: React.ReactNode; isAccordionHeader?: boolean; styles: any; theme: any; }
interface TeamColourSelectorProps { teamLabel: string; kitColors: KitColors; onPress: () => void; styles: any; }
interface KitColorPickerModalProps { visible: boolean; onClose: () => void; onSave: (colors: KitColors) => void; initialColors: KitColors; styles: any; modalStyles: any; }
interface PickerLikeRowProps { label: string; value: string; options?: string[]; onValueChange: (value: string) => void; styles: any; theme: any; }
interface NumericInputRowProps { label: string; value: number; onValueChange: (val: number) => void; styles: any; theme: any; }

// Reusable Components
const FormFieldRow: React.FC<FormFieldRowProps> = ({ label, value, placeholder, children, showArrow, onPress, isEditable, onChangeText, keyboardType, autoCapitalize, arrowIcon, isAccordionHeader, styles, theme }) => ( <TouchableOpacity style={[styles.inputField, isAccordionHeader && styles.accordionHeaderField]} activeOpacity={onPress && !isEditable ? 0.7 : 1} onPress={isEditable ? undefined : onPress}><Text style={styles.inputLabel}>{label}</Text><View style={styles.inputValueContainer}>{isEditable ? ( <TextInput style={styles.textInput} value={value?.toString()} onChangeText={onChangeText} placeholder={placeholder} placeholderTextColor="#A0A0A0" keyboardType={keyboardType} autoCapitalize={autoCapitalize}/>) : children ? children : ( <Text style={value || value === 0 ? styles.inputValueText : styles.inputPlaceholderTextStatic}>{value !== undefined && value !== null ? String(value) : placeholder}</Text>)}{(showArrow || isAccordionHeader) && (arrowIcon || <ChevronDownIcon color={theme.text} />)}</View></TouchableOpacity> );
const PickerLikeRow: React.FC<PickerLikeRowProps> = ({ label, value, options = ['Yes', 'No'], onValueChange, styles, theme }) => { const cycleValue = () => { const currentIndex = options.indexOf(value); const nextIndex = (currentIndex + 1) % options.length; onValueChange(options[nextIndex]); }; return <FormFieldRow label={label} value={value} onPress={cycleValue} showArrow={true} styles={styles} theme={theme} />; };
const NumericInputRow: React.FC<NumericInputRowProps> = ({ label, value, onValueChange, styles, theme }) => ( <View style={styles.inputField}><Text style={styles.inputLabel}>{label}</Text><View style={styles.numericInputControls}><TouchableOpacity onPress={() => onValueChange(value - 1)} style={styles.numericButton}><MinusIcon size={14} color={theme.text} /></TouchableOpacity><Text style={styles.numericValueText}>{value}</Text><TouchableOpacity onPress={() => onValueChange(value + 1)} style={styles.numericButton}><PlusIcon size={14} color={theme.text} /></TouchableOpacity></View></View> );
const TeamColourSelector: React.FC<TeamColourSelectorProps> = ({ teamLabel, kitColors, onPress, styles }) => ( <View style={styles.inputField}><Text style={styles.inputLabel}>{teamLabel}</Text><TouchableOpacity style={styles.kitIconWrapper} onPress={onPress}><StripedTshirt baseColor={kitColors.base} stripeColor={kitColors.stripe} size={28} /></TouchableOpacity></View> );
const KitColorPickerModal: React.FC<KitColorPickerModalProps> = ({ visible, onClose, onSave, initialColors, styles, modalStyles }) => {
  const [baseColor, setBaseColor] = useState(initialColors.base);
  const [stripeColor, setStripeColor] = useState(initialColors.stripe);
  const handleSave = () => { onSave({ base: baseColor, stripe: stripeColor }); onClose(); };
  useEffect(() => { setBaseColor(initialColors.base); setStripeColor(initialColors.stripe); }, [initialColors]);
  const renderColorOptions = (setColor: Dispatch<SetStateAction<string>>, selectedColor: string) => ( <View style={modalStyles.colorGrid}>{colorPalette.map(color => ( <TouchableOpacity key={color} style={[modalStyles.colorSwatch, { backgroundColor: color, borderWidth: selectedColor === color ? 2 : 1, borderColor: selectedColor === color ? styles.theme.primary : styles.theme.border }]} onPress={() => setColor(color)} /> ))}</View> );
  return ( <Modal animationType="fade" transparent={true} visible={visible} onRequestClose={onClose}><View style={modalStyles.modalOverlay}><View style={modalStyles.modalContent}><Text style={modalStyles.modalTitle}>Select Kit Colours</Text><View style={modalStyles.pickerContainer}><View style={modalStyles.previewContainer}><StripedTshirt baseColor={baseColor} stripeColor={stripeColor} size={120} /></View><View style={modalStyles.palettesContainer}><Text style={modalStyles.paletteTitle}>Base Color</Text>{renderColorOptions(setBaseColor, baseColor)}<Text style={modalStyles.paletteTitle}>Stripe Color</Text>{renderColorOptions(setStripeColor, stripeColor)}</View></View><View style={modalStyles.buttonContainer}><TouchableOpacity style={[modalStyles.button, modalStyles.cancelButton]} onPress={onClose}><Text style={modalStyles.cancelButtonText}>Cancel</Text></TouchableOpacity><TouchableOpacity style={[modalStyles.button, modalStyles.saveButton]} onPress={handleSave}><Text style={modalStyles.saveButtonText}>Save</Text></TouchableOpacity></View></View></View></Modal> );
};

// --- Form Content Component ---
const MatchForm: React.FC = () => {
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);
  const modalStyles = getModalStyles(theme);

  // State
  const [competition, setCompetition] = useState('');
  const [venue, setVenue] = useState('');
  const [homeTeamName, setHomeTeamName] = useState('');
  const [homeTeamShortName, setHomeTeamShortName] = useState('');
  const [awayTeamName, setAwayTeamName] = useState('');
  const [awayTeamShortName, setAwayTeamShortName] = useState('');
  const [homeKitColors, setHomeKitColors] = useState<KitColors>({ base: '#FF0000', stripe: '#00FFFF' });
  const [awayKitColors, setAwayKitColors] = useState<KitColors>({ base: '#FFFFFF', stripe: '#0000FF' });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingKitFor, setEditingKitFor] = useState<'home' | 'away' | null>(null);
  const [isTemplateSectionExpanded, setIsTemplateSectionExpanded] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [newTemplateName, setNewTemplateName] = useState('');
  const [isTemplatePickerVisible, setTemplatePickerVisible] = useState(false);
  const [numberOfPeriods, setNumberOfPeriods] = useState(2);
  const [periodLengths, setPeriodLengths] = useState<number[]>([45, 45]);
  const [extraTimeLengths, setExtraTimeLengths] = useState<[number, number]>([15, 15]);
  const [teamSize, setTeamSize] = useState(11);
  const [benchSize, setBenchSize] = useState(5);
  const [halfTime, setHalfTime] = useState(15);
  const [extraTime, setExtraTime] = useState('No');
  const [SubOpportunities, setSubOpportunities] = useState('No');
  const [SubOpportunitiesAllowance, setSubOpportunitiesAllowance] = useState(1);
  const [extraTimeSubOpportunities, setExtraTimeSubOpportunities] = useState('No');
  const [extraTimeSubOpportunitiesAllowance, setExtraTimeSubOpportunitiesAllowance] = useState(1);
  const [penalties, setPenalties] = useState('No');
  const [misconductCode, setMisconductCode] = useState('England');
  const [temporaryDismissals, setTemporaryDismissals] = useState('No');
  const [temporaryDismissalsTime, setTemporaryDismissalsTime] = useState(10);
  const [isEarningsExpanded, setIsEarningsExpanded] = useState(false);
  const [fees, setFees] = useState('');
  const [expenses, setExpenses] = useState('');
  const [mileage, setMileage] = useState('');
  const [travelTime, setTravelTime] = useState('');
  const [isNoteExpanded, setIsNoteExpanded] = useState(false);
  const [noteContent, setNoteContent] = useState('');
  const [isMatchOfficialsExpanded, setIsMatchOfficialsExpanded] = useState(false);
  const [matchOfficialsList, setMatchOfficialsList] = useState<Official[]>([ { id: generateId(), role: 'Referee', name: 'Dave John' }, { id: generateId(), role: 'Ar 1', name: '-----' },{ id: generateId(), role: 'Ar 2', name: '-----' },{ id: generateId(), role: '4th', name: '-----' }, ]);
  const [injuryTimeAllowance, setInjuryTimeAllowance] = useState('No');
  const [injuryTime, setInjuryTime] = useState({ subs: 0, sanctions: 0, goals: 0 });

  useEffect(() => {
    const count = numberOfPeriods;
    if (count > 0 && count !== periodLengths.length) {
      const newLengths = Array.from({ length: count }, (_, i) => periodLengths[i] || 45);
      setPeriodLengths(newLengths);
    }
  }, [numberOfPeriods]);

  // Handlers
  const openColorPicker = (team: 'home' | 'away') => { setEditingKitFor(team); setIsModalVisible(true); };
  const handleSaveKitColors = (newColors: KitColors) => { if (editingKitFor === 'home') { setHomeKitColors(newColors); } else if (editingKitFor === 'away') { setAwayKitColors(newColors); }};
  const toggleSection = (expandedSetter: Dispatch<SetStateAction<boolean>>, isCurrentlyExpanded: boolean) => { LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut); expandedSetter(!isCurrentlyExpanded); };
  const handleAddOfficial = () => { LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut); setMatchOfficialsList([...matchOfficialsList, { id: generateId(), role: '', name: '' }]); };
  const handleRemoveOfficial = (idToRemove: number) => { LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut); setMatchOfficialsList(matchOfficialsList.filter(official => official.id !== idToRemove)); };
  const handleOfficialChange = (id: number, field: keyof Omit<Official, 'id'>, value: string) => { setMatchOfficialsList(matchOfficialsList.map(official => (official.id === id ? { ...official, [field]: value } : official))); };
  const getInitialColorsForModal = (): KitColors => { if (editingKitFor === 'home') return homeKitColors; if (editingKitFor === 'away') return awayKitColors; return { base: '#FFFFFF', stripe: '#000000' }; };
  
  return (
    <>
      <ScrollView contentContainerStyle={styles.scrollContentContainer} showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled">
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Create Match</Text>
          <FormFieldRow styles={styles} theme={theme} label="Competition" placeholder="Competition" isEditable={true} value={competition} onChangeText={setCompetition} autoCapitalize="words"/>
          <FormFieldRow styles={styles} theme={theme} label="Venue" placeholder="Venue" isEditable={true} value={venue} onChangeText={setVenue} autoCapitalize="words"/>
          <FormFieldRow styles={styles} theme={theme} label="Select date" value="03/12/2025, 9:00AM" onPress={() => console.log("Select Date Tapped")}/>
          <FormFieldRow styles={styles} theme={theme} label="Official role" value="Referee" onPress={() => console.log("Official Role Tapped")}/>
        </View>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Match Format</Text>
          <View style={styles.subSectionWrapper}>
            <Text style={styles.subSectionTitle}>Home Team</Text>
            <FormFieldRow styles={styles} theme={theme} label="Team Name" placeholder="Home team" isEditable={true} value={homeTeamName} onChangeText={setHomeTeamName} autoCapitalize="words"/>
            <FormFieldRow styles={styles} theme={theme} label="Short name" placeholder="HOM" isEditable={true} value={homeTeamShortName} onChangeText={setHomeTeamShortName} autoCapitalize="characters"/>
            <TeamColourSelector styles={styles} teamLabel="Team Colour" kitColors={homeKitColors} onPress={() => openColorPicker('home')} />
          </View>
          <View style={styles.subSectionWrapper}>
            <Text style={styles.subSectionTitle}>Away Team</Text>
            <FormFieldRow styles={styles} theme={theme} label="Team Name" placeholder="Away team" isEditable={true} value={awayTeamName} onChangeText={setAwayTeamName} autoCapitalize="words"/>
            <FormFieldRow styles={styles} theme={theme} label="Short name" placeholder="AWY" isEditable={true} value={awayTeamShortName} onChangeText={setAwayTeamShortName} autoCapitalize="characters"/>
            <TeamColourSelector styles={styles} teamLabel="Team Colour" kitColors={awayKitColors} onPress={() => openColorPicker('away')} />
          </View>
        </View>
        <View style={styles.sectionContainer}>
            <FormFieldRow styles={styles} theme={theme} label="Select Template" onPress={() => toggleSection(setIsTemplateSectionExpanded, isTemplateSectionExpanded)} isAccordionHeader={true} arrowIcon={isTemplateSectionExpanded ? <ChevronUpIcon color={theme.text} /> : <ChevronDownIcon color={theme.text} />}/>
            {isTemplateSectionExpanded && ( 
              <View style={styles.accordionContentContainer}>
                <View style={styles.templateInputContainer}>
                  <FormFieldRow styles={styles} theme={theme} label="Template" value={selectedTemplate === CREATE_NEW_TEMPLATE ? 'Create New' : selectedTemplate} placeholder="Select or Create" onPress={() => setTemplatePickerVisible(!isTemplatePickerVisible)} showArrow={true} />
                  {isTemplatePickerVisible && (
                    <View style={styles.templateDropdown}>
                      <TouchableOpacity style={styles.dropdownItem} onPress={() => { setSelectedTemplate(CREATE_NEW_TEMPLATE); setNewTemplateName(''); setTemplatePickerVisible(false); }}>
                        <Text style={styles.dropdownItemText}>+ Create New</Text>
                      </TouchableOpacity>
                      {preSavedTemplates.map((template) => (
                        <TouchableOpacity key={template} style={styles.dropdownItem} onPress={() => { setSelectedTemplate(template); setTemplatePickerVisible(false); }}>
                          <Text style={styles.dropdownItemText}>{template}</Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>
                {selectedTemplate === CREATE_NEW_TEMPLATE && (<FormFieldRow styles={styles} theme={theme} label="New Template Name" placeholder="Enter template name" isEditable={true} value={newTemplateName} onChangeText={setNewTemplateName} autoCapitalize="words" />)}
                <FormFieldRow styles={styles} theme={theme} label="Team size" isEditable={true} value={String(teamSize)} onChangeText={(val: string) => setTeamSize(Number(val) || 0)} keyboardType="number-pad"/>
                <FormFieldRow styles={styles} theme={theme} label="Bench size" isEditable={true} value={String(benchSize)} onChangeText={(val: string) => setBenchSize(Number(val) || 0)} keyboardType="number-pad"/>
                <NumericInputRow styles={styles} theme={theme} label="Number of periods" value={numberOfPeriods} onValueChange={(newValue) => { if (newValue >= 1) setNumberOfPeriods(newValue); }} />
                {periodLengths.map((length, index) => ( <NumericInputRow key={`period-${index}`} styles={styles} theme={theme} label={`Period ${index + 1} Length`} value={length} onValueChange={(newValue) => { const newLengths = [...periodLengths]; newLengths[index] = newValue >= 0 ? newValue : 0; setPeriodLengths(newLengths); }} /> ))}
                <PickerLikeRow styles={styles} theme={theme} label="Injury time allowance" value={injuryTimeAllowance} onValueChange={setInjuryTimeAllowance} />
                {injuryTimeAllowance === 'Yes' && (
                  <View>
                    <NumericInputRow styles={styles} theme={theme} label="Subs (s)" value={injuryTime.subs} onValueChange={(newValue) => setInjuryTime(prev => ({...prev, subs: Math.max(0, newValue)}))}/>
                    <NumericInputRow styles={styles} theme={theme} label="Sanctions (s)" value={injuryTime.sanctions} onValueChange={(newValue) => setInjuryTime(prev => ({...prev, sanctions: Math.max(0, newValue)}))}/>
                    <NumericInputRow styles={styles} theme={theme} label="Goals (s)" value={injuryTime.goals} onValueChange={(newValue) => setInjuryTime(prev => ({...prev, goals: Math.max(0, newValue)}))}/>
                  </View>
                )}
                <PickerLikeRow styles={styles} theme={theme} label="Extra Time" value={extraTime} onValueChange={setExtraTime} />
                {extraTime === 'Yes' && (
                  <View>
                    <NumericInputRow styles={styles} theme={theme} label="ET Period 1 Length" value={extraTimeLengths[0]} onValueChange={(newValue) => setExtraTimeLengths([newValue >= 0 ? newValue : 0, extraTimeLengths[1]])} />
                    <NumericInputRow styles={styles} theme={theme} label="ET Period 2 Length" value={extraTimeLengths[1]} onValueChange={(newValue) => setExtraTimeLengths([extraTimeLengths[0], newValue >= 0 ? newValue : 0])} />
                  </View>
                )}
                <TouchableOpacity style={styles.inputField} activeOpacity={0.7} onPress={() => setSubOpportunities(SubOpportunities === 'Yes' ? 'No' : 'Yes')}><Text style={styles.longLabel}>Additional sub opportunities</Text><View style={styles.pickerValueContainer}><Text style={styles.inputValueText}>{SubOpportunities}</Text><ChevronDownIcon color={theme.text}/></View></TouchableOpacity>
                {SubOpportunities === 'Yes' && (<NumericInputRow styles={styles} theme={theme} label="Sub opportunities allowance" value={SubOpportunitiesAllowance} onValueChange={(val) => setSubOpportunitiesAllowance(val)} />)}
                <TouchableOpacity style={styles.inputField} activeOpacity={0.7} onPress={() => setExtraTimeSubOpportunities(extraTimeSubOpportunities === 'Yes' ? 'No' : 'Yes')}><Text style={styles.longLabel}>Extra time additional sub opportunities</Text><View style={styles.pickerValueContainer}><Text style={styles.inputValueText}>{extraTimeSubOpportunities}</Text><ChevronDownIcon color={theme.text}/></View></TouchableOpacity>
                {extraTimeSubOpportunities === 'Yes' && (<NumericInputRow styles={styles} theme={theme} label="Extra time sub opportunities allowance" value={extraTimeSubOpportunitiesAllowance} onValueChange={(val) => setExtraTimeSubOpportunitiesAllowance(val)} />)}
                <PickerLikeRow styles={styles} theme={theme} label="Penalties" value={penalties} onValueChange={setPenalties} />
                <PickerLikeRow styles={styles} theme={theme} label="Misconduct code" value={misconductCode} onValueChange={setMisconductCode} options={['England', 'Scotland', 'Wales']} />
                <PickerLikeRow styles={styles} theme={theme} label="Temporary dismissals" value={temporaryDismissals} onValueChange={setTemporaryDismissals} />
                {temporaryDismissals === 'Yes' && (<NumericInputRow styles={styles} theme={theme} label="Temporary dismissals time" value={temporaryDismissalsTime} onValueChange={(val) => setTemporaryDismissalsTime(val)} />)}
              </View> 
            )}
            <View style={styles.matchOfficialsHeaderContainer}>
                <FormFieldRow styles={styles} theme={theme} label="Match officials" onPress={() => toggleSection(setIsMatchOfficialsExpanded, isMatchOfficialsExpanded)} isAccordionHeader={true} arrowIcon={isMatchOfficialsExpanded ? <ChevronUpIcon color={theme.text}/> : <ChevronDownIcon color={theme.text}/>} />
                {isMatchOfficialsExpanded && ( <TouchableOpacity onPress={handleAddOfficial} style={styles.addOfficialButton}><UserPlusIcon color={theme.primary} /></TouchableOpacity> )}
            </View>
            {isMatchOfficialsExpanded && ( <View style={styles.accordionContentContainer}>{matchOfficialsList.map((official) => ( <View key={official.id} style={styles.officialRow}><TextInput style={styles.officialRoleInput} value={official.role} onChangeText={(text) => handleOfficialChange(official.id, 'role', text)} placeholder="Role" placeholderTextColor="#A0A0A0" /><TextInput style={styles.officialNameInput} value={official.name} onChangeText={(text) => handleOfficialChange(official.id, 'name', text)} placeholder="Name or '-----'" placeholderTextColor="#A0A0A0" /><TouchableOpacity onPress={() => handleRemoveOfficial(official.id)} style={styles.removeOfficialButton}><CloseCircleIcon color={theme.text} /></TouchableOpacity></View>))}</View> )}
            <FormFieldRow styles={styles} theme={theme} label="Earnings" onPress={() => toggleSection(setIsEarningsExpanded, isEarningsExpanded)} isAccordionHeader={true} arrowIcon={isEarningsExpanded ? <ChevronUpIcon color={theme.text}/> : <ChevronDownIcon color={theme.text}/>}/>
            {isEarningsExpanded && ( <View style={styles.accordionContentContainer}><FormFieldRow styles={styles} theme={theme} label="Fees" placeholder="Fees" isEditable={true} value={fees} onChangeText={setFees} keyboardType="decimal-pad"/><FormFieldRow styles={styles} theme={theme} label="Expenses" placeholder="Expenses" isEditable={true} value={expenses} onChangeText={setExpenses} keyboardType="decimal-pad"/><FormFieldRow styles={styles} theme={theme} label="Mileage" placeholder="Mileage" isEditable={true} value={mileage} onChangeText={setMileage} keyboardType="decimal-pad"/><FormFieldRow styles={styles} theme={theme} label="Travel Time" placeholder="Travel Time" isEditable={true} value={travelTime} onChangeText={setTravelTime} keyboardType="decimal-pad"/></View> )}
            <FormFieldRow styles={styles} theme={theme} label="Note" onPress={() => toggleSection(setIsNoteExpanded, isNoteExpanded)} isAccordionHeader={true} arrowIcon={isNoteExpanded ? <ChevronUpIcon color={theme.text}/> : <ChevronDownIcon color={theme.text}/>}/>
            {isNoteExpanded && ( <View style={[styles.accordionContentContainer, styles.noteInputContainer]}><TextInput style={styles.noteTextInput} value={noteContent} onChangeText={setNoteContent} placeholder="Enter your note here..." placeholderTextColor="#A0A0A0" multiline={true} numberOfLines={4}/></View> )}
        </View>
      </ScrollView>
      <KitColorPickerModal visible={isModalVisible} onClose={() => setIsModalVisible(false)} onSave={handleSaveKitColors} initialColors={getInitialColorsForModal()} styles={{theme}} modalStyles={modalStyles} />
    </>
  );
};

// --- Main Screen Component ---
const CreateMatchScreen = () => {
    const { theme } = useTheme();
    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: theme.card }}>
            <Header title="Create Match" showBackButton={true} />
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                <MatchForm />
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
};

// --- DYNAMIC STYLESHEETS ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  scrollContentContainer: { padding: 15, paddingBottom: 30, backgroundColor: theme.background },
  sectionContainer: { 
      backgroundColor: theme.card, 
      borderRadius: 12, 
      paddingHorizontal: 12, 
      paddingTop: 12, 
      paddingBottom: 7, 
      marginBottom: 15, 
      shadowColor: isDarkMode ? 'transparent' : "#000", 
      shadowOffset: { width: 0, height: 1, }, 
      shadowOpacity: 0.10, 
      shadowRadius: 2.00, 
      elevation: 2,
      borderWidth: isDarkMode ? 1 : 0,
      borderColor: theme.border,
  },
  sectionTitle: { fontSize: 16, fontWeight: 'bold', color: theme.text, marginBottom: 10, paddingLeft: 3 },
  subSectionWrapper: { backgroundColor: `${theme.text}08`, borderRadius: 10, paddingHorizontal: 10, paddingTop: 10, paddingBottom: 5, marginBottom: 12 },
  subSectionTitle: { fontSize: 14, fontWeight: '600', color: theme.text, marginBottom: 8, paddingLeft: 3 },
  inputField: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', backgroundColor: 'transparent', borderRadius: 8, borderWidth: 1, borderColor: theme.border1, paddingHorizontal: 12, minHeight: 50, marginBottom: 10, },
  accordionHeaderField: {},
  inputLabel: { fontSize: 14, color: theme.text, fontWeight: '400', marginRight: 10, flexShrink: 1 },
  longLabel: { flex: 1, marginRight: 10, fontSize: 14, color: theme.text, fontWeight: '400', flexWrap: 'wrap' },
  pickerValueContainer: { flexDirection: 'row', alignItems: 'center', },
  inputValueContainer: { flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end' },
  textInput: { flex: 1, fontSize: 14, color: theme.text, opacity: 0.8, textAlign: 'right', paddingVertical: Platform.OS === 'ios' ? 14 : 10, paddingHorizontal: 0 },
  inputValueText: { fontSize: 14, color: theme.text, opacity: 0.8, textAlign: 'right', },
  inputPlaceholderTextStatic: { fontSize: 14, color: '#A0A0A0', textAlign: 'right' },
  kitIconWrapper: { padding: 5, },
  accordionContentContainer: { paddingTop: 10, },
  templateInputContainer: { position: 'relative', zIndex: 10, },
  templateDropdown: { position: 'absolute', top: 52, left: 0, right: 0, backgroundColor: theme.card, borderColor: theme.border, borderWidth: 1, borderRadius: 8, shadowColor: "#000", shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.15, shadowRadius: 3.84, elevation: 5, },
  dropdownItem: { paddingHorizontal: 12, paddingVertical: 14, borderBottomWidth: 1, borderBottomColor: theme.border, },
  dropdownItemText: { fontSize: 14, color: theme.text, opacity: 0.8, },
  numericInputControls: { flexDirection: 'row', alignItems: 'center' },
  numericButton: { padding: 8, marginHorizontal: 5, borderWidth: 1, borderColor: theme.border, borderRadius: 15, width: 30, height: 30, justifyContent: 'center', alignItems: 'center', },
  numericValueText: { fontSize: 14, color: theme.text, opacity: 0.8, minWidth: 30, textAlign: 'center', marginHorizontal: 5 },
  noteInputContainer: {},
  noteTextInput: { backgroundColor: `${theme.text}08`, borderColor: theme.border, borderWidth: 1, borderRadius: 8, padding: 12, fontSize: 14, color: theme.text, textAlignVertical: 'top', minHeight: 100, marginBottom: 10, },
  matchOfficialsHeaderContainer: { position: 'relative' },
  addOfficialButton: { position: 'absolute', right: 40, top: 0, bottom: 0, justifyContent: 'center', alignItems: 'center', paddingHorizontal: 5, zIndex: 1, },
  officialRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: 'transparent', borderRadius: 8, borderWidth: 1, borderColor: theme.border, paddingHorizontal: 10, minHeight: 50, marginBottom: 8 },
  officialRoleInput: { flex: 0.8, fontSize: 14, color: theme.text, paddingVertical: Platform.OS === 'ios' ? 14 : 10, marginRight: 5 },
  officialNameInput: { flex: 1, fontSize: 14, color: theme.text, opacity: 0.8, textAlign: 'right', paddingVertical: Platform.OS === 'ios' ? 14 : 10, marginRight: 5 },
  removeOfficialButton: { padding: 8 },
});

const getModalStyles = (theme: any) => StyleSheet.create({
  modalOverlay: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.6)' },
  modalContent: { width: '90%', backgroundColor: theme.card, borderRadius: 15, padding: 20, shadowColor: "#000", shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.25, shadowRadius: 4, elevation: 5, borderWidth: 1, borderColor: theme.border },
  modalTitle: { fontSize: 20, fontWeight: 'bold', color: theme.text, marginBottom: 20, textAlign: 'center' },
  pickerContainer: { flexDirection: 'row', marginBottom: 20, },
  previewContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 10, },
  palettesContainer: { flex: 1.5, paddingLeft: 15 },
  paletteTitle: { fontSize: 16, fontWeight: '600', color: theme.text, marginBottom: 8 },
  colorGrid: { flexDirection: 'row', flexWrap: 'wrap', marginBottom: 15 },
  colorSwatch: { width: 30, height: 30, borderRadius: 15, margin: 4, },
  buttonContainer: { flexDirection: 'row', justifyContent: 'space-between', paddingTop: 10, borderTopWidth: 1, borderTopColor: theme.border },
  button: { flex: 1, paddingVertical: 12, borderRadius: 8, alignItems: 'center', marginHorizontal: 5 },
  cancelButton: { backgroundColor: theme.border },
  cancelButtonText: { color: theme.text, fontSize: 16, fontWeight: 'bold' },
  saveButton: { backgroundColor: theme.primary },
  saveButtonText: { color: '#FFFFFF', fontSize: 16, fontWeight: 'bold' },
});

export default CreateMatchScreen;