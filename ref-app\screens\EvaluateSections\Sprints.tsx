import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions, DimensionValue, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext'; // Assuming this is the correct path to your ThemeContext

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// --- Consistent Color Palette (will be overridden by theme) ---
const AppColors = {
  primaryGreen: '#068B47',
  lowIntensity: '#068B47',
  mediumIntensity: '#FF7B00',
  highIntensity: '#FF0404',
};

// --- Component Interfaces ---
interface SprintCardItem {
  value: string | number;
  label: string;
}

interface SprintCardProps {
  title: string;
  data: SprintCardItem[];
  styles: any; // Pass styles as a prop
  theme: any; // Pass theme as a prop
}

interface BarChartItem {
  value: number;
  label:string;
  color: string;
  labelValue: string;
}

interface BarChartProps {
  title: string;
  description?: string;
  data: BarChartItem[];
  chartTopMargin?: DimensionValue;
  styles: any; // Pass styles as a prop
}

interface Match {
  name: string;
  date: string;
}

interface TopMatchesProps {
  matches: Match[];
  styles: any; // Pass styles as a prop
}

// --- MODIFIED Speed Zoning Interface ---
interface SpeedZones {
  lowEnd: number;
  mediumEnd: number;
  highEnd: number;
}
interface SpeedZoningCardProps {
    isEditing: boolean;
    zones: SpeedZones;
    onZoneChange: (zoneKey: keyof SpeedZones, amount: number) => void;
    lowZoneStart: number;
    onToggleEdit: () => void;
    styles: any; // Pass styles as a prop
    theme: any; // Pass theme as a prop
}

// --- Data and Constants ---
const matchData = [
    { team1: 'PSG', team2: 'MAN', first: 6, second: 12, third: 10 },
    { team1: 'PSG', team2: 'MAN', first: 3, second: 20, third: 32 },
    { team1: 'PSG', team2: 'MAN', first: 6, second: 12, third: 16 },
    { team1: 'PSG', team2: 'MAN', first: 6, second: 12, third: 13 },
    { team1: 'PSG', team2: 'MAN', first: 6, second: 12, third: 6 },
];

const MAX_DISTANCE = 60;

// --- UI Components ---

const SprintCard: React.FC<SprintCardProps> = ({ title, data, styles, theme }) => (
  <View style={styles.card}>
    <View style={styles.centeredHeaderContainer}>
      <Text style={styles.dataCardTitle}>{title}</Text>
    </View>
    <View style={styles.cardRow}>
      {data.map((item, index) => (
        <View key={index} style={styles.cardItem}>
          <Text style={[styles.itemValue, { color: theme.primary }]}>{item.value}</Text>
          <Text style={styles.itemLabel}>{item.label}</Text>
        </View>
      ))}
    </View>
  </View>
);

const BarChart: React.FC<BarChartProps> = ({
  data,
  title,
  description,
  chartTopMargin,
  styles,
}) => (
  <View style={styles.card}>
    <View style={styles.centeredHeaderContainer}>
      <Text style={styles.cardTitle}>{title}</Text>
      {description ? (
        <Text style={styles.chartDescription}>{description}</Text>
      ) : null}
    </View>
    <View style={[styles.simpleBarChartContainer, { marginTop: chartTopMargin ?? 25 }]}>
      {data.map((item, index) => (
        <View key={index} style={styles.simpleBarWrapper}>
          <View style={[styles.barLabelTop, { backgroundColor: item.color }]}>
            <Text style={styles.barLabelText}>{item.labelValue}</Text>
          </View>
          <View style={styles.simpleBarContainer}>
            <View
              style={[
                styles.simpleBar,
                { height: item.value, backgroundColor: item.color },
              ]}
            />
          </View>
          <Text style={styles.barLabelBottom}>{item.label}</Text>
        </View>
      ))}
    </View>
  </View>
);


const EditControlRow: React.FC<{
    label: string;
    rangeText: string;
    onDecrement: () => void;
    onIncrement: () => void;
    styles: any;
    theme: any;
}> = ({ label, rangeText, onDecrement, onIncrement, styles, theme }) => (
    <View style={styles.editRow}>
        <Text style={styles.editLabel}>{label}</Text>
        <View style={styles.editControls}>
            <TouchableOpacity onPress={onDecrement} style={styles.editButton}>
                <Text style={styles.editButtonText}>-</Text>
            </TouchableOpacity>
            <Text style={[styles.editValueText, { color: theme.primary }]}>{rangeText}</Text>
            <TouchableOpacity onPress={onIncrement} style={styles.editButton}>
                <Text style={styles.editButtonText}>+</Text>
            </TouchableOpacity>
        </View>
    </View>
);


const SpeedZoningCard: React.FC<SpeedZoningCardProps> = ({ isEditing, zones, onZoneChange, lowZoneStart, onToggleEdit, styles, theme }) => {
    return (
        <View style={styles.card}>
            <View style={styles.cardHeaderWithButton}>
                <Text style={styles.cardTitle}>{isEditing ? 'Edit Speed Zoning' : 'Speed Zoning'}</Text>
                <TouchableOpacity onPress={onToggleEdit} style={[styles.toggleEditButton, { backgroundColor: theme.primary }]}>
                    <Text style={styles.toggleEditButtonText}>{isEditing ? 'Done' : 'Edit'}</Text>
                </TouchableOpacity>
            </View>

            {isEditing ? (
                <>
                    <EditControlRow
                        label="Low"
                        rangeText={`${lowZoneStart.toFixed(1)} - ${zones.lowEnd.toFixed(1)} km/h`}
                        onDecrement={() => onZoneChange('lowEnd', -0.1)}
                        onIncrement={() => onZoneChange('lowEnd', 0.1)}
                        styles={styles}
                        theme={theme}
                    />
                    <EditControlRow
                        label="Medium"
                        rangeText={`${zones.lowEnd.toFixed(1)} - ${zones.mediumEnd.toFixed(1)} km/h`}
                        onDecrement={() => onZoneChange('mediumEnd', -0.1)}
                        onIncrement={() => onZoneChange('mediumEnd', 0.1)}
                        styles={styles}
                        theme={theme}
                    />
                    <EditControlRow
                        label="High"
                        rangeText={`${zones.mediumEnd.toFixed(1)} - ${zones.highEnd.toFixed(1)} km/h`}
                        onDecrement={() => onZoneChange('highEnd', -0.1)}
                        onIncrement={() => onZoneChange('highEnd', 0.1)}
                        styles={styles}
                        theme={theme}
                    />
                </>
            ) : (
                <>
                    <View style={styles.speedRangeTitleContainer}>
                        <Text style={styles.speedRangeLabel}>Low</Text>
                        <Text style={styles.speedRangeLabel}>Medium</Text>
                        <Text style={styles.speedRangeLabel}>High</Text>
                    </View>
                    <View style={styles.speedRangeContainer}>
                        <View style={[styles.speedRangeBar, { backgroundColor: AppColors.lowIntensity }]} />
                        <View style={[styles.speedRangeBar, { backgroundColor: AppColors.mediumIntensity }]} />
                        <View style={[styles.speedRangeBar, { backgroundColor: AppColors.highIntensity }]} />
                    </View>
                    <View style={styles.speedRangeTitleContainer}>
                        <Text style={styles.speedRangeValue}>{`${lowZoneStart.toFixed(1)}–${zones.lowEnd.toFixed(1)} km/h`}</Text>
                        <Text style={styles.speedRangeValue}>{`${zones.lowEnd.toFixed(1)}–${zones.mediumEnd.toFixed(1)} km/h`}</Text>
                        <Text style={styles.speedRangeValue}>{`${zones.mediumEnd.toFixed(1)}–${zones.highEnd.toFixed(1)} km/h`}</Text>
                    </View>
                </>
            )}
        </View>
    );
};


const TopMatches: React.FC<TopMatchesProps> = ({ matches, styles }) => (
  <View style={styles.card}>
    <View style={styles.centeredHeaderContainer}>
        <Text style={styles.cardTitle}>
            {matches.length > 0 ? 'Top matches' : 'Top match'}
        </Text>
    </View>
    {matches.map((match, index) => (
      <View key={index} style={styles.matchRow}>
        <Text style={styles.matchText}>{match.name}</Text>
        <Text style={styles.matchDate}>{match.date}</Text>
      </View>
    ))}
  </View>
);

const LastMatchesChart = ({ styles }: { styles: any }) => {
    const barAreaHeight = 175;
    const yAxisLabels = Array.from({ length: (MAX_DISTANCE / 10) + 1 }, (_, i) => i * 10);

    return (
        <View style={styles.chartContainer}>
            <View style={styles.chartHeader}>
                <Text style={styles.lastMatchesChartTitle}>Last 5 Matches</Text>
            </View>

            <View style={styles.chartBody}>
                <View style={styles.yAxisArea}>
                    <View style={styles.yAxisLabelWrapper}>
                        <View style={styles.rotatedLabelContainer}>
                            <Text style={styles.yAxisLabelText}>Number of sprints</Text>
                        </View>
                    </View>
                    <View style={styles.yAxisLabelsContainer}>
                        {yAxisLabels.map((label) => (
                            <Text
                                key={label}
                                style={[
                                    styles.yAxisTickLabel,
                                    { bottom: (label / MAX_DISTANCE) * barAreaHeight - 6 }
                                ]}
                            >
                                {label}
                            </Text>
                        ))}
                    </View>
                </View>

                <View style={styles.mainChartArea}>
                    <View style={styles.barsArea}>
                        {matchData.map((match, index) => {
                            const orangeValue = match.first;
                            const redValue = match.second;
                            const greenValue = match.third;
                            const totalValue = orangeValue + redValue + greenValue;
                            const orangeHeightPx = (orangeValue / MAX_DISTANCE) * barAreaHeight;
                            const redHeightPx = (redValue / MAX_DISTANCE) * barAreaHeight;
                            const greenHeightPx = (greenValue / MAX_DISTANCE) * barAreaHeight;
                            const totalBarHeightPx = (totalValue / MAX_DISTANCE) * barAreaHeight;

                            return (
                                <View key={index} style={styles.barWrapper}>
                                    <View style={[styles.barStack, { height: totalBarHeightPx }]}>
                                        <View style={{ height: greenHeightPx, backgroundColor: AppColors.highIntensity }} />
                                        <View style={{ height: redHeightPx, backgroundColor: AppColors.mediumIntensity }} />
                                        <View style={{ height: orangeHeightPx, backgroundColor: AppColors.lowIntensity }} />
                                    </View>
                                </View>
                            );
                        })}
                    </View>
                    <View style={styles.xAxisLabelsArea}>
                        {matchData.map((match, i) => (
                            <View key={i} style={styles.xAxisLabelWrapper}>
                                <Text style={styles.xAxisLabel} numberOfLines={1} adjustsFontSizeToFit minimumFontScale={0.7}>
                                    {match.team1}
                                </Text>
                                <Text style={styles.xAxisLabelDash}>-</Text>
                                <Text style={styles.xAxisLabel} numberOfLines={1} adjustsFontSizeToFit minimumFontScale={0.7}>
                                    {match.team2}
                                </Text>
                            </View>
                        ))}
                    </View>
                </View>
            </View>
            
            <View style={styles.legendContainer}>
                <View style={styles.legendItem}>
                    <View style={[styles.legendColor, { backgroundColor: AppColors.lowIntensity }]} />
                    <Text style={styles.legendText}>Low</Text>
                </View>
                <View style={styles.legendItem}>
                    <View style={[styles.legendColor, { backgroundColor: AppColors.mediumIntensity }]} />
                    <Text style={styles.legendText}>Medium</Text>
                </View>
                <View style={styles.legendItem}>
                    <View style={[styles.legendColor, { backgroundColor: AppColors.highIntensity }]} />
                    <Text style={styles.legendText}>High</Text>
                </View>
            </View>
        </View>
    );
};


const Sprints = () => {
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);

  const [isEditing, setIsEditing] = useState(false);
  const [speedZones, setSpeedZones] = useState<SpeedZones>({
    lowEnd: 19.7,
    mediumEnd: 25.1,
    highEnd: 43.2,
  });
  const LOW_ZONE_START = 16.2;

  const handleZoneChange = (zoneKey: keyof SpeedZones, amount: number) => {
    setSpeedZones(prevZones => {
        const potentialNewValue = prevZones[zoneKey] + amount;
        const newValue = Math.max(0, Math.round(potentialNewValue * 10) / 10);

        if (zoneKey === 'lowEnd') {
            if (newValue <= LOW_ZONE_START || newValue >= prevZones.mediumEnd) return prevZones;
        }
        if (zoneKey === 'mediumEnd') {
            if (newValue <= prevZones.lowEnd || newValue >= prevZones.highEnd) return prevZones;
        }
        if (zoneKey === 'highEnd') {
            if (newValue <= prevZones.mediumEnd) return prevZones;
        }

        return { ...prevZones, [zoneKey]: newValue };
    });
  };

  const totalSprints = [ { value: 18, label: 'Low' }, { value: 7, label: 'Medium' }, { value: 2, label: 'High' } ];
  const averageSprints = [ { value: '18.00', label: 'Low' }, { value: '7.00', label: 'Medium' }, { value: '2.00', 'label': 'High' } ];
  const averageDistance = [ { value: '0.26 km', label: 'Low' }, { value: '0.20 km', 'label': 'Medium' }, { value: '0.04 km', label: 'High' } ];
  const topMatchesData = [ { name: 'Old Matches v New city', date: '05/21/2025' } ];

  const activityBreakdownData = [
    { value: 110, label: 'Walk', color: AppColors.lowIntensity, labelValue: '66 %' },
    { value: 140, label: 'Run', color: AppColors.mediumIntensity, labelValue: '85 %' },
    { value: 17, label: 'Sprint', color: AppColors.highIntensity, labelValue: '10 %' }
  ];
  const distanceCoveredData = [
    { value: 100, label: 'Walk', color: AppColors.lowIntensity, labelValue: '60km' },
    { value: 140, label: 'Run', color: AppColors.mediumIntensity, labelValue: '85km' },
    { value: 55, label: 'Sprint', color: AppColors.highIntensity, labelValue: '35km' }
  ];

  return (
    <ScrollView
      style={styles.screenContainer}
      showsVerticalScrollIndicator={false}>
      <View style={styles.contentContainer}>
        <SprintCard title="Total" data={totalSprints} styles={styles} theme={theme} />
        <SprintCard title="Avg Efforts per Zone" data={averageSprints} styles={styles} theme={theme} />
        <SprintCard title="Avg Distance per Zone" data={averageDistance} styles={styles} theme={theme} />
        
        <SpeedZoningCard 
            isEditing={isEditing}
            zones={speedZones}
            onZoneChange={handleZoneChange}
            lowZoneStart={LOW_ZONE_START}
            onToggleEdit={() => setIsEditing(!isEditing)}
            styles={styles}
            theme={theme}
        />
        
        <BarChart title="Activity Breakdown" description="Percentage of match by speed" data={activityBreakdownData} chartTopMargin={'16%'} styles={styles} />
        <BarChart title="Distance covered by speed" description="" data={distanceCoveredData} chartTopMargin={'16%'} styles={styles} />
        
        <TopMatches matches={topMatchesData} styles={styles} />
        <View style={styles.enlargedCardWrapper}>
            <LastMatchesChart styles={styles} />
        </View>
      </View>
    </ScrollView>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
    screenContainer: { flex: 1, backgroundColor: theme.background },
    contentContainer: { padding: 15, paddingHorizontal: 10 },
    cardHeaderWithButton: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    toggleEditButton: {
        paddingHorizontal: 16,
        paddingVertical: 6,
        borderRadius: 20,
    },
    toggleEditButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    card: {
        backgroundColor: theme.card,
        borderRadius: 12,
        padding: 15,
        marginBottom: 15,
        borderWidth: isDarkMode ? 1 : 0,
        borderColor: theme.border,
    },
    centeredHeaderContainer: {
        alignItems: 'center',
        marginBottom: 16,
    },
    cardTitle: { fontSize: 16, fontWeight: 'bold', color: theme.text },
    chartDescription: { fontSize: 13, color: theme.text, marginTop: 5 },
    dataCardTitle: { fontSize: 13, color: theme.text, fontWeight: '600' },
    cardRow: { flexDirection: 'row', justifyContent: 'space-around' },
    cardItem: { alignItems: 'center', flex: 1 },
    itemValue: { fontSize: 18, fontWeight: 'bold', marginBottom: 2, textAlign: 'center' },
    itemLabel: { fontSize: 11, color: theme.text, textAlign: 'center' },
    simpleBarChartContainer: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'center',
        height: 180,
    },
    simpleBarWrapper: {
        flex: 1,
        alignItems: 'center',
        maxWidth: 80,
    },
    simpleBarContainer: {
        height: 150,
        width: 20,
        justifyContent: 'flex-end',
    },
    simpleBar: {
        width: '100%',
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5,
    },
    barLabelTop: {
        borderRadius: 8,
        paddingHorizontal: 8,
        paddingVertical: 4,
        marginBottom: 8,
    },
    barLabelText: {
        fontSize: 12,
        fontWeight: 'bold',
        color: 'white',
        textAlign: 'center',
    },
    barLabelBottom: {
      marginTop: 5,
      fontSize: 11,
      color: theme.text
    },
    speedRangeTitleContainer: { flexDirection: 'row', justifyContent: 'space-between', paddingHorizontal: 5 },
    speedRangeContainer: { flexDirection: 'row', height: 15, borderRadius: 10, overflow: 'hidden', marginVertical: 5 },
    speedRangeLabel: { fontSize: 13, fontWeight: 'bold', color: theme.text },
    speedRangeBar: { flex: 1, height: '100%' },
    speedRangeValue: { fontSize: 11, color: theme.text },
    matchRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 10, borderBottomWidth: 1, borderBottomColor: theme.border },
    matchText: { fontSize: 13, color: theme.text },
    matchDate: { fontSize: 13, color: theme.text },
    enlargedCardWrapper: {
        marginHorizontal: -10,
        marginBottom: 15,
    },
    chartContainer: {
        backgroundColor: theme.card,
        padding: 15,
        borderRadius: 20,
        shadowColor: isDarkMode ? 'transparent' : '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
        elevation: 5,
        borderWidth: isDarkMode ? 1 : 0,
        borderColor: theme.border,
    },
    chartHeader: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 15,
        paddingHorizontal: 8,
    },
    lastMatchesChartTitle: {
        fontSize: 15,
        fontWeight: 'bold',
        color: theme.text
    },
    legendContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 15,
        paddingTop: 10,
        borderTopWidth: 1,
        borderTopColor: theme.border
    },
    legendItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginHorizontal: 15
    },
    legendColor: {
        width: 12,
        height: 12,
        borderRadius: 2,
        marginRight: 4
    },
    legendText: {
        fontSize: 11,
        color: theme.text,
        fontWeight: '500'
    },
    chartBody: {
        flexDirection: 'row',
        height: 220,
    },
    yAxisArea: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
    },
    yAxisLabelWrapper: {
        width: 25,
        height: 220,
        justifyContent: 'center',
        alignItems: 'center',
    },
    rotatedLabelContainer: {
        position: 'absolute',
        width: 220,
        height: 25,
        justifyContent: 'center',
        transform: [{ rotate: '-90deg' }],
    },
    yAxisLabelText: {
        fontSize: 12,
        fontWeight: '500',
        color: theme.text,
        textAlign: 'center'
    },
    yAxisLabelsContainer: {
        width: 30,
        height: 175,
        position: 'relative',
        marginBottom: 45,
    },
    yAxisTickLabel: {
        position: 'absolute',
        right: 5,
        fontSize: 12,
        color: theme.text,
        fontWeight: 'bold',
    },
    mainChartArea: {
        flex: 1,
        justifyContent: 'flex-end'
    },
    barsArea: {
        flexDirection: 'row',
        height: 175,
        paddingHorizontal: 3
    },
    barWrapper: {
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'center',
        marginHorizontal: 1,
    },
    barStack: {
        width: '35%',
        flexDirection: 'column-reverse',
        overflow: 'hidden',
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
        minWidth: 12
    },
    xAxisLabelsArea: {
        flexDirection: 'row',
        height: 45,
        paddingTop: 3,
        paddingHorizontal: 3
    },
    xAxisLabelWrapper: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'flex-start',
        marginHorizontal: 1,
        paddingHorizontal: 1,
    },
    xAxisLabel: {
        fontSize: SCREEN_WIDTH < 350 ? 9 : 10,
        color: theme.text,
        opacity: 0.89,
        textAlign: 'center',
        lineHeight: SCREEN_WIDTH < 350 ? 11 : 12,
        fontWeight: '600',
        letterSpacing: -0.2,
        height: 14,
    },
    xAxisLabelDash: {
        fontSize: SCREEN_WIDTH < 350 ? 8 : 9,
        color: theme.text,
        opacity: 0.6,
        textAlign: 'center',
        lineHeight: SCREEN_WIDTH < 350 ? 10 : 11,
        fontWeight: '400',
        height: 10,
    },
    editRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 10,
      borderBottomWidth: 1,
      borderBottomColor: theme.border,
    },
    editLabel: {
        fontSize: 16,
        fontWeight: 'bold',
        color: theme.text,
        flex: 0.8,
    },
    editControls: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        flex: 2,
    },
    editButton: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: isDarkMode ? theme.card : '#eeeeee',
        justifyContent: 'center',
        alignItems: 'center',
    },
    editButtonText: {
        fontSize: 22,
        fontWeight: 'bold',
        color: theme.text,
        lineHeight: 24,
    },
    editValueText: {
        fontSize: 14,
        fontWeight: '600',
        minWidth: 130,
        textAlign: 'center',
        marginHorizontal: 5,
    },
});

export default Sprints;