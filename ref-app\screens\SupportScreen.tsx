import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct

// --- TYPE DEFINITIONS ---
type SectionItem = { 
  label: string; 
  value?: string; 
  arrow?: boolean; 
  onPress?: () => void;
};

type SectionProps = { 
  title?: string; 
  items: SectionItem[];
  styles: any; // Pass styles from getStyles
  theme: any; // Pass theme object
};

// --- REUSABLE SECTION COMPONENT (Now receives styles and theme) ---
const Section = ({ title, items, styles, theme }: SectionProps) => (
  <View style={styles.sectionBox}>
    {title && <Text style={styles.sectionTitle}>{title}</Text>}
    {items.map((item: SectionItem, idx: number) => (
      <TouchableOpacity
        key={item.label}
        style={[styles.row, idx === items.length - 1 && { borderBottomWidth: 0 }]}
        activeOpacity={0.7}
        onPress={item.onPress}
      >
        <Text style={styles.rowLabel}>{item.label}</Text>
        <View style={styles.rowRight}>
          {item.value && <Text style={styles.rowValue}>{item.value}</Text>}
          {item.arrow && <MaterialIcons name="chevron-right" size={22} color={theme.text} style={{ opacity: 0.6 }} />}
        </View>
      </TouchableOpacity>
    ))}
  </View>
);

const SupportScreen = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const styles = getStyles(theme);

  const supportItems: SectionItem[] = [
    { 
      label: 'Frequently Asked Questions', 
      arrow: true,
      onPress: () => navigation.navigate('FAQ' as never)
    },
    { 
      label: 'Feature Requests', 
      arrow: true,
      onPress: () => navigation.navigate('FeatureRequests' as never)
    },
    { 
      label: 'Watch Debugger', 
      arrow: true,
      onPress: () => navigation.navigate('WatchDebugger' as never)
    },
    { 
      label: 'Contact us', 
      arrow: true,
      onPress: () => navigation.navigate('ContactUs' as never)
    },
    { 
      label: 'Terms and conditions', 
      arrow: true,
      onPress: () => navigation.navigate('TermsConditions' as never)
    },
    { 
      label: 'Privacy Policy', 
      arrow: true,
      onPress: () => navigation.navigate('PrivacyPolicy' as never)
    },
  ];

  return (
    <View style={styles.container}>
      <Header title="Support" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <Section items={supportItems} styles={styles} theme={theme} />
      </ScrollView>
    </View>
  );
};

// --- DYNAMIC STYLESHEET ---
const getStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background, // Changed from hardcoded color
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
    paddingTop: 16, // Added padding top for consistency
  },
  sectionBox: {
    backgroundColor: theme.card,
    borderRadius: 12,
    borderWidth: 1, // Use a consistent border
    borderColor: theme.border,
    marginBottom: 18,
    marginTop: 8,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontWeight: '700',
    fontSize: 14,
    color: theme.text,
    backgroundColor: theme.card,
    paddingHorizontal: 14,
    paddingTop: 12,
    paddingBottom: 6,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 13,
    paddingHorizontal: 14,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
    backgroundColor: theme.card,
  },
  rowLabel: {
    fontSize: 15,
    color: theme.text,
    fontWeight: '500',
    flex: 1,
  },
  rowRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowValue: {
    fontSize: 15,
    color: theme.text,
    opacity: 0.7,
    fontWeight: '500',
    marginRight: 8,
  },
});

export default SupportScreen;