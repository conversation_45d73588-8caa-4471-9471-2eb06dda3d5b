import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, LayoutAnimation } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct

const faqItems = [
  { 
    question: 'How do I reset my password?',
    answer: 'To reset your password, go to the login screen and tap on "Forgot Password". Follow the instructions sent to your email.'
  },
  { 
    question: 'How do I update my profile information?',
    answer: 'You can update your profile information by going to the Settings screen and selecting "Edit Profile".'
  },
  { 
    question: 'How do I contact support?',
    answer: 'You can contact our support team through the Contact Us section in the Support screen.'
  },
  { 
    question: 'Is there a mobile app available?',
    answer: 'Yes, our mobile app is available for both iOS and Android devices.'
  },
  { 
    question: 'How do I delete my account?',
    answer: 'To delete your account, go to Settings > Account > Delete Account. Please note this action cannot be undone.'
  },
];

const FAQScreen = () => {
  const { theme } = useTheme();
  const styles = getStyles(theme);
  const [expandedItem, setExpandedItem] = useState<number | null>(null);

  const toggleItem = (index: number) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpandedItem(expandedItem === index ? null : index);
  };

  return (
    <View style={styles.container}>
      <Header title="FAQ" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.sectionBox}>
          {faqItems.map((item, index) => (
            <View key={index}>
              <TouchableOpacity
                style={[styles.row, index === faqItems.length - 1 && expandedItem !== index && { borderBottomWidth: 0 }]}
                onPress={() => toggleItem(index)}
                activeOpacity={0.7}
              >
                <Text style={styles.questionText}>{item.question}</Text>
                <MaterialIcons 
                  name={expandedItem === index ? 'keyboard-arrow-up' : 'keyboard-arrow-down'} 
                  size={24} 
                  color={theme.text}
                  style={{ opacity: 0.6 }}
                />
              </TouchableOpacity>
              {expandedItem === index && (
                <View style={styles.answerContainer}>
                  <Text style={styles.answerText}>{item.answer}</Text>
                </View>
              )}
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const getStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background, // Use theme background for the main container
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
    paddingTop: 16,
  },
  sectionBox: {
    backgroundColor: theme.card,
    borderRadius: 12,
    borderWidth: 1, // Use a consistent border
    borderColor: theme.border,
    overflow: 'hidden',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
    backgroundColor: theme.card,
  },
  questionText: {
    flex: 1,
    fontSize: 15,
    color: theme.text,
    fontWeight: '500',
    marginRight: 12,
  },
  answerContainer: {
    padding: 16,
    backgroundColor: theme.background, // Use main background for a subtle contrast
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  answerText: {
    fontSize: 14,
    color: theme.text,
    opacity: 0.8,
    lineHeight: 20,
  },
});

export default FAQScreen;