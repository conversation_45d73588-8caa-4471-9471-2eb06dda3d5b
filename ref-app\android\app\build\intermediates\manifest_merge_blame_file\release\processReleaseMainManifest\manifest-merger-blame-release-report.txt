1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.refrate.refereetracker"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:2:3-64
11-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:2:20-62
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:3:3-77
12-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:3:20-75
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:4:3-75
13-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:4:20-73
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:5:3-63
14-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:5:20-61
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:6:3-78
15-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:6:20-76
16
17    <queries>
17-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:7:3-13:13
18        <intent>
18-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:8:5-12:14
19            <action android:name="android.intent.action.VIEW" />
19-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:7-58
19-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:15-56
20
21            <category android:name="android.intent.category.BROWSABLE" />
21-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:7-67
21-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:17-65
22
23            <data android:scheme="https" />
23-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:7-37
23-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:13-35
24        </intent>
25
26        <package android:name="com.facebook.katana" /> <!-- Query open documents -->
26-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:16:9-55
26-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:16:18-52
27        <intent>
27-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
28            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
28-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
28-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
29        </intent>
30    </queries>
31
32    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Support for Google Privacy Sandbox adservices API -->
32-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:14:5-79
32-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:14:22-76
33    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
33-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:16:5-88
33-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:16:22-85
34    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
34-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:17:5-82
34-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:17:22-79
35    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
35-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:18:5-92
35-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:18:22-89
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
36-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:19:5-83
36-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:19:22-80
37
38    <permission
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.refrate.refereetracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.refrate.refereetracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
43-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b8d9f7f8cd67980f1dd3adb74bef188\transformed\installreferrer-1.0\AndroidManifest.xml:9:5-110
43-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b8d9f7f8cd67980f1dd3adb74bef188\transformed\installreferrer-1.0\AndroidManifest.xml:9:22-107
44
45    <application
45-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:3-46:17
46        android:name="com.refrate.refereetracker.MainApplication"
46-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:16-47
47        android:allowBackup="true"
47-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:162-188
48        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
49        android:extractNativeLibs="false"
50        android:icon="@mipmap/ic_launcher"
50-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:81-115
51        android:label="@string/app_name"
51-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:48-80
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:116-161
53        android:supportsRtl="true"
53-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:221-247
54        android:theme="@style/AppTheme" >
54-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:189-220
55        <meta-data
55-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:15:5-101
56            android:name="com.facebook.sdk.AdvertiserIDCollectionEnabled"
56-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:15:16-77
57            android:value="false" />
57-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:15:78-99
58        <meta-data
58-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:16:5-103
59            android:name="com.facebook.sdk.ApplicationId"
59-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:16:16-61
60            android:value="@string/facebook_app_id" />
60-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:16:62-101
61        <meta-data
61-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:17:5-92
62            android:name="com.facebook.sdk.ApplicationName"
62-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:17:16-63
63            android:value="mobile-app" />
63-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:17:64-90
64        <meta-data
64-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:18:5-86
65            android:name="com.facebook.sdk.AutoInitEnabled"
65-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:18:16-63
66            android:value="true" />
66-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:18:64-84
67        <meta-data
67-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:19:5-95
68            android:name="com.facebook.sdk.AutoLogAppEventsEnabled"
68-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:19:16-71
69            android:value="false" />
69-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:19:72-93
70        <meta-data
70-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:20:5-107
71            android:name="com.facebook.sdk.ClientToken"
71-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:20:16-59
72            android:value="@string/facebook_client_token" />
72-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:20:60-105
73        <meta-data
73-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:21:5-83
74            android:name="expo.modules.updates.ENABLED"
74-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:21:16-59
75            android:value="false" />
75-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:21:60-81
76        <meta-data
76-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:22:5-105
77            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
77-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:22:16-80
78            android:value="ALWAYS" />
78-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:22:81-103
79        <meta-data
79-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:23:5-99
80            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
80-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:23:16-79
81            android:value="0" />
81-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:23:80-97
82
83        <activity
83-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:5-36:16
84            android:name="com.refrate.refereetracker.MainActivity"
84-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:15-43
85            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
85-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:44-134
86            android:exported="true"
86-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:256-279
87            android:launchMode="singleTask"
87-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:135-166
88            android:screenOrientation="portrait"
88-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:280-316
89            android:theme="@style/Theme.App.SplashScreen"
89-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:210-255
90            android:windowSoftInputMode="adjustResize" >
90-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:167-209
91            <intent-filter>
91-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:25:7-28:23
92                <action android:name="android.intent.action.MAIN" />
92-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:26:9-60
92-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:26:17-58
93
94                <category android:name="android.intent.category.LAUNCHER" />
94-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:27:9-68
94-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:27:19-66
95            </intent-filter>
96            <intent-filter>
96-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:29:7-35:23
97                <action android:name="android.intent.action.VIEW" />
97-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:7-58
97-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:15-56
98
99                <category android:name="android.intent.category.DEFAULT" />
99-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:31:9-67
99-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:31:19-65
100                <category android:name="android.intent.category.BROWSABLE" />
100-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:7-67
100-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:17-65
101
102                <data android:scheme="com.refrate.refereetracker" />
102-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:7-37
102-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:13-35
103                <data android:scheme="exp+mobile-app" />
103-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:7-37
103-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:13-35
104            </intent-filter>
105        </activity>
106        <activity
106-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:37:5-178
107            android:name="com.facebook.FacebookActivity"
107-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:37:15-59
108            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
108-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:37:60-143
109            android:label="@string/app_name"
109-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:37:144-176
110            android:theme="@style/com_facebook_activity_theme" />
110-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:23:13-63
111        <activity
111-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:38:5-45:16
112            android:name="com.facebook.CustomTabActivity"
112-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:38:15-60
113            android:exported="true" >
113-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:38:61-84
114            <intent-filter>
114-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:39:7-44:23
115                <action android:name="android.intent.action.VIEW" />
115-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:7-58
115-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:15-56
116
117                <category android:name="android.intent.category.DEFAULT" />
117-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:31:9-67
117-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:31:19-65
118                <category android:name="android.intent.category.BROWSABLE" />
118-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:7-67
118-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:17-65
119
120                <data android:scheme="@string/fb_login_protocol_scheme" />
120-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:7-37
120-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:13-35
121            </intent-filter>
122            <intent-filter>
122-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
123                <action android:name="android.intent.action.VIEW" />
123-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:7-58
123-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:15-56
124
125                <category android:name="android.intent.category.DEFAULT" />
125-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:31:9-67
125-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:31:19-65
126                <category android:name="android.intent.category.BROWSABLE" />
126-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:7-67
126-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:17-65
127
128                <data
128-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:7-37
129                    android:host="cct.com.refrate.refereetracker"
130                    android:scheme="fbconnect" />
130-->C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:13-35
131            </intent-filter>
132        </activity>
133
134        <property
134-->[:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:9-47:48
135            android:name="android.adservices.AD_SERVICES_CONFIG"
135-->[:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-65
136            android:resource="@xml/ad_services_config" />
136-->[:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:13-55
137
138        <meta-data
138-->[:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
139            android:name="org.unimodules.core.AppLoader#react-native-headless"
139-->[:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
140            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
140-->[:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
141        <meta-data
141-->[:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
142            android:name="com.facebook.soloader.enabled"
142-->[:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
143            android:value="true" />
143-->[:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
144
145        <activity
145-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
146            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
146-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
147            android:excludeFromRecents="true"
147-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
148            android:exported="false"
148-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
149            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
149-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
150        <!--
151            Service handling Google Sign-In user revocation. For apps that do not integrate with
152            Google Sign-In, this service will never be started.
153        -->
154        <service
154-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
155            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
155-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
156            android:exported="true"
156-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
157            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
157-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
158            android:visibleToInstantApps="true" />
158-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
159
160        <activity android:name="com.facebook.CustomTabMainActivity" />
160-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:24:9-71
160-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:24:19-68
161        <activity
161-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
162            android:name="com.google.android.gms.common.api.GoogleApiActivity"
162-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
163            android:exported="false"
163-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
164            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
164-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
165
166        <meta-data
166-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
167            android:name="com.google.android.gms.version"
167-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
168            android:value="@integer/google_play_services_version" />
168-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
169
170        <provider
170-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
171            android:name="expo.modules.filesystem.FileSystemFileProvider"
171-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
172            android:authorities="com.refrate.refereetracker.FileSystemFileProvider"
172-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
173            android:exported="false"
173-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
174            android:grantUriPermissions="true" >
174-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
175            <meta-data
175-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-29:70
176                android:name="android.support.FILE_PROVIDER_PATHS"
176-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-67
177                android:resource="@xml/file_system_provider_paths" />
177-->[:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-67
178        </provider>
179        <!--
180         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
181         with the application context. This config is merged in with the host app's manifest,
182         but there can only be one provider with the same authority activated at any given
183         point; so if the end user has two or more different apps that use Facebook SDK, only the
184         first one will be able to use the provider. To work around this problem, we use the
185         following placeholder in the authority to identify each host application as if it was
186         a completely different provider.
187        -->
188        <provider
188-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
189            android:name="com.facebook.internal.FacebookInitProvider"
189-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:33:13-70
190            android:authorities="com.refrate.refereetracker.FacebookInitProvider"
190-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:34:13-72
191            android:exported="false" />
191-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:35:13-37
192
193        <receiver
193-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
194            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
194-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:38:13-86
195            android:exported="false" >
195-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:39:13-37
196            <intent-filter>
196-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
197                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
197-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:41:17-95
197-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:41:25-92
198            </intent-filter>
199        </receiver>
200        <receiver
200-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
201            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
201-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:45:13-118
202            android:exported="false" >
202-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:46:13-37
203            <intent-filter>
203-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
204                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
204-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:48:17-103
204-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:48:25-100
205            </intent-filter>
206        </receiver>
207
208        <provider
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
209            android:name="androidx.startup.InitializationProvider"
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
210            android:authorities="com.refrate.refereetracker.androidx-startup"
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
211            android:exported="false" >
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
212            <meta-data
212-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
213                android:name="androidx.emoji2.text.EmojiCompatInitializer"
213-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
214                android:value="androidx.startup" />
214-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
215            <meta-data
215-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
216                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
216-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
217                android:value="androidx.startup" />
217-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
218            <meta-data
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
219                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
220                android:value="androidx.startup" />
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
221        </provider>
222
223        <receiver
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
224            android:name="androidx.profileinstaller.ProfileInstallReceiver"
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
225            android:directBootAware="false"
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
226            android:enabled="true"
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
227            android:exported="true"
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
228            android:permission="android.permission.DUMP" >
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
230                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
231            </intent-filter>
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
233                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
234            </intent-filter>
235            <intent-filter>
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
236                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
237            </intent-filter>
238            <intent-filter>
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
239                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
240            </intent-filter>
241        </receiver>
242    </application>
243
244</manifest>
