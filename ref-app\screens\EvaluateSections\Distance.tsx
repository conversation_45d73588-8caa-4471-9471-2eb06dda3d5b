import React from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

// Get screen dimensions for responsive design
const { width: screenWidth } = Dimensions.get('window');

// Define a constant for colors that do not change with the theme
const AppColors = {
    Orange: '#FF7B00',
};

// Define the structure for match data
interface MatchData {
    team1: string;
    team2: string;
    first: number;
    second: number;
}

// Data for all matches
const matchData: MatchData[] = [
    { team1: 'PSG', team2: 'MAN', first: 19, second: 8.5 },
    { team1: 'LIV', team2: 'TOT', first: 13, second: 4.5 },
    { team1: 'UNI', team2: 'MAR', first: 19, second: 5 },
    { team1: 'FAN', team2: 'WMM', first: 13, second: 11 },
    { team1: 'PSG', team2: 'MAN', first: 19, second: 5 },
    { team1: 'LIV', team2: 'TOT', first: 13, second: 8.5 },
    { team1: 'UNI', team2: 'MAR', first: 19, second: 8.5 },
    { team1: 'FAN', team2: 'CIT', first: 13, second: 11 },
    { team1: 'UNI', team2: 'MAR', first: 19, second: 8 },
    { team1: 'FAN', team2: 'CIT', first: 13, second: 11 },
];

const MAX_DISTANCE = 27.5; // Adjusted to match the tallest bar in the dataset

const Distance = () => {
    // Use the theme context to get current theme and dark mode status
    const { theme, isDarkMode } = useTheme();
    // Generate styles based on the current theme
    const styles = getStyles(theme, isDarkMode);
    // Select only the first 5 matches for the chart
    const last5MatchesData = matchData.slice(0, 5);

    return (
        <View style={styles.outerContainer}>
            <ScrollView contentContainerStyle={styles.scrollContentContainer} showsVerticalScrollIndicator={false}>
                {/* Distance Summary Card */}
                <View style={[styles.card, { marginBottom: 20 }]}>
                    <Text style={styles.cardTitle}>Distance</Text>
                    <View style={styles.row}>
                        <View style={styles.statItem}>
                            <Text style={styles.statValue}>74,01 mi</Text>
                            <Text style={styles.statLabel}>Total</Text>
                        </View>
                        <View style={styles.statItem}>
                            <Text style={styles.statValue}>3.50</Text>
                            <Text style={styles.statLabel}>Avg</Text>
                        </View>
                    </View>
                </View>

                {/* Halves Breakdown */}
                <View style={[styles.card, { marginBottom: 20 }]}>
                    <Text style={styles.halfLabel}>1st</Text>
                    <View style={styles.row}>
                        <View style={styles.statItem}>
                            <Text style={styles.statValue}>100 mi</Text>
                            <Text style={styles.statLabel}>Total</Text>
                        </View>
                        <View style={styles.statItem}>
                            <Text style={styles.statValue}>30.51 mi</Text>
                            <Text style={styles.statLabel}>Avg</Text>
                        </View>
                    </View>
                    <Text style={[styles.halfLabel, { marginTop: 25 }]}>2nd</Text>
                    <View style={styles.row}>
                        <View style={styles.statItem}>
                            <Text style={styles.statValue}>10 mi</Text>
                            <Text style={styles.statLabel}>Total</Text>
                        </View>
                        <View style={styles.statItem}>
                            <Text style={styles.statValue}>3.51 mi</Text>
                            <Text style={styles.statLabel}>Avg</Text>
                        </View>
                    </View>
                </View>

                {/* Pass the sliced data, styles, and theme to the chart component */}
                <LastMatchesChart data={last5MatchesData} styles={styles} theme={theme} />
            </ScrollView>
        </View>
    );
};

// Define props for the LastMatchesChart component
interface LastMatchesChartProps {
    data: MatchData[];
    styles: any;
    theme: any;
}

const LastMatchesChart = ({ data, styles, theme }: LastMatchesChartProps) => {
    const yAxisLabels = ["25", "20", "15", "10", "5", "0"];

    return (
        <View style={styles.chartContainer}>
            <View style={styles.chartHeader}>
                <Text style={styles.chartTitle}>Last 5 Matches</Text>
                <View style={styles.legendContainer}>
                    <View style={styles.legendItem}>
                        <View style={[styles.legendColor, { backgroundColor: theme.primary }]} />
                        <Text style={styles.legendText}>1st</Text>
                    </View>
                    <View style={styles.legendItem}>
                        <View style={[styles.legendColor, { backgroundColor: AppColors.Orange }]} />
                        <Text style={styles.legendText}>2nd</Text>
                    </View>
                </View>
            </View>

            <View style={styles.chartBody}>
                {/* Y-Axis with Rotated Label */}
                <View style={styles.yAxisContainer}>
                    <View style={styles.yAxisLabelWrapper}>
                        <View style={styles.rotatedLabelContainer}>
                            <Text style={styles.yAxisLabelText} numberOfLines={1} allowFontScaling={false}>
                                Distance / mi
                            </Text>
                        </View>
                    </View>
                    <View style={styles.yAxisLabels}>
                        {yAxisLabels.map((label) => (
                            <Text key={label} style={styles.yAxisTickText}>{label}</Text>
                        ))}
                    </View>
                </View>

                {/* Bars Area and X-Axis */}
                <View style={styles.mainChartArea}>
                    <View style={styles.barsArea}>
                        {data.map((match, index) => {
                            const totalHeight = match.first + match.second;
                            const firstH = totalHeight > 0 ? (match.first / totalHeight) * 100 : 0;
                            const secondH = totalHeight > 0 ? (match.second / totalHeight) * 100 : 0;
                            return (
                                <View key={index} style={styles.barWrapper}>
                                    <View style={[styles.barStack, { height: `${(totalHeight / MAX_DISTANCE) * 100}%` }]}>
                                        <View style={{ height: `${secondH}%`, backgroundColor: AppColors.Orange }} />
                                        <View style={{ height: `${firstH}%`, backgroundColor: theme.primary }} />
                                    </View>
                                </View>
                            );
                        })}
                    </View>
                    <View style={styles.xAxisLabelsArea}>
                        {data.map((match, i) => (
                            <View key={i} style={styles.xAxisLabelWrapper}>
                                <Text style={styles.xAxisLabel} numberOfLines={1} adjustsFontSizeToFit minimumFontScale={0.7}>
                                    {match.team1}
                                </Text>
                                <Text style={styles.xAxisLabelDash}>-</Text>
                                <Text style={styles.xAxisLabel} numberOfLines={1} adjustsFontSizeToFit minimumFontScale={0.7}>
                                    {match.team2}
                                </Text>
                            </View>
                        ))}
                    </View>
                </View>
            </View>
        </View>
    );
};

// Function to generate styles based on the current theme
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
    outerContainer: {
        flex: 1,
        backgroundColor: isDarkMode ? theme.background : '#f5f5f5',
    },
    scrollContentContainer: {
        padding: 10,
        paddingHorizontal: 5
    },
    card: {
        backgroundColor: theme.card,
        borderRadius: 16,
        padding: 20,
        marginHorizontal: 5,
        shadowColor: isDarkMode ? 'transparent' : '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        borderColor: theme.border,
        borderWidth: isDarkMode ? 1 : 0,
    },
    cardTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
        color: theme.text,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-around'
    },
    statItem: {
        alignItems: 'center'
    },
    statValue: {
        fontSize: 20,
        fontWeight: 'bold',
        color: theme.primary,
    },
    statLabel: {
        fontSize: 12,
        color: theme.text,
        opacity: 0.7,
        marginTop: 4
    },
    halfLabel: {
        textAlign: 'center',
        fontSize: 14,
        color: theme.text,
        opacity: 0.7,
        marginBottom: 10
    },
    chartContainer: {
        backgroundColor: theme.card,
        marginHorizontal: 0,
        padding: 15,
        paddingHorizontal: 8,
        borderRadius: 20,
        shadowColor: isDarkMode ? 'transparent' : '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 5,
        elevation: 5,
        borderColor: theme.border,
        borderWidth: isDarkMode ? 1 : 0,
    },
    chartHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15
    },
    chartTitle: {
        fontSize: 15,
        fontWeight: 'bold',
        color: theme.text,
    },
    legendContainer: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    legendItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 12
    },
    legendColor: {
        width: 12,
        height: 12,
        borderRadius: 2,
        marginRight: 4
    },
    legendText: {
        fontSize: 11,
        color: theme.text,
        fontWeight: '500'
    },
    chartBody: {
        flexDirection: 'row',
        height: 220
    },
    yAxisContainer: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        overflow: 'visible'
    },
    yAxisLabelWrapper: {
        width: 25,
        height: 220,
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'visible'
    },
    rotatedLabelContainer: {
        position: 'absolute',
        width: 220,
        height: 25,
        justifyContent: 'center',
        transform: [{ rotate: '-90deg' }]
    },
    yAxisLabelText: {
        fontSize: 12,
        fontWeight: '500',
        color: theme.text,
        textAlign: 'center'
    },
    yAxisLabels: {
        justifyContent: 'space-between',
        paddingRight: 8,
        paddingBottom: 40,
        height: '100%',
        paddingTop: 0
    },
    yAxisTickText: {
        fontSize: 12,
        color: theme.text,
        fontWeight: 'bold',
        textAlign: 'right'
    },
    mainChartArea: {
        flex: 1,
        justifyContent: 'flex-end'
    },
    barsArea: {
        flexDirection: 'row',
        flex: 1,
        borderBottomWidth: 1.5,
        borderBottomColor: theme.text,
        paddingHorizontal: 3
    },
    barWrapper: {
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'center',
        marginHorizontal: 1
    },
    barStack: {
        width: '35%',
        flexDirection: 'column-reverse',
        overflow: 'hidden',
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
        minWidth: 12
    },
    xAxisLabelsArea: {
        flexDirection: 'row',
        height: 45,
        paddingTop: 3,
        paddingHorizontal: 3
    },
    xAxisLabelWrapper: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'flex-start',
        marginHorizontal: 1,
        paddingHorizontal: 1,
    },
    xAxisLabel: {
        fontSize: screenWidth < 350 ? 9 : 10,
        color: theme.text,
        opacity: 0.89,
        textAlign: 'center',
        lineHeight: screenWidth < 350 ? 11 : 12,
        fontWeight: '600',
        letterSpacing: -0.2,
        height: 14,
    },
    xAxisLabelDash: {
        fontSize: screenWidth < 350 ? 8 : 9,
        color: theme.text,
        opacity: 0.6,
        textAlign: 'center',
        lineHeight: screenWidth < 350 ? 10 : 11,
        fontWeight: '400',
        height: 10,
    }
});

export default Distance;