{"logs": [{"outputFile": "com.refrate.refereetracker.app-mergeReleaseResources-54:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9a19d447f99dacc987e9045d6bd5cdca\\transformed\\browser-1.6.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,104", "endOffsets": "156,259,369,474"}, "to": {"startLines": "67,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6780,7113,7216,7326", "endColumns": "105,102,109,104", "endOffsets": "6881,7211,7321,7426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6550f91160cccf4761958ba318c6d009\\transformed\\material-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1071,1138,1235,1304,1367,1454,1518,1584,1644,1713,1774,1828,1943,2002,2062,2116,2188,2318,2406,2485,2583,2671,2755,2893,2971,3047,3186,3280,3360,3416,3470,3536,3609,3687,3758,3842,3915,3993,4066,4141,4251,4341,4416,4510,4608,4682,4759,4859,4912,4996,5064,5153,5242,5304,5369,5432,5502,5609,5709,5809,5905,5965,6023,6103,6193,6268", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "268,346,420,504,597,691,790,915,1003,1066,1133,1230,1299,1362,1449,1513,1579,1639,1708,1769,1823,1938,1997,2057,2111,2183,2313,2401,2480,2578,2666,2750,2888,2966,3042,3181,3275,3355,3411,3465,3531,3604,3682,3753,3837,3910,3988,4061,4136,4246,4336,4411,4505,4603,4677,4754,4854,4907,4991,5059,5148,5237,5299,5364,5427,5497,5604,5704,5804,5900,5960,6018,6098,6188,6263,6344"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,68,69,70,75,78,80,81,82,83,84,85,86,87,88,89,90,91,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3101,3179,3253,3337,3430,4248,4347,4472,6886,6949,7016,7503,7738,7869,7956,8020,8086,8146,8215,8276,8330,8445,8504,8564,8618,8690,9046,9134,9213,9311,9399,9483,9621,9699,9775,9914,10008,10088,10144,10198,10264,10337,10415,10486,10570,10643,10721,10794,10869,10979,11069,11144,11238,11336,11410,11487,11587,11640,11724,11792,11881,11970,12032,12097,12160,12230,12337,12437,12537,12633,12693,12751,13237,13327,13402", "endLines": "5,34,35,36,37,38,46,47,48,68,69,70,75,78,80,81,82,83,84,85,86,87,88,89,90,91,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "318,3174,3248,3332,3425,3519,4342,4467,4555,6944,7011,7108,7567,7796,7951,8015,8081,8141,8210,8271,8325,8440,8499,8559,8613,8685,8815,9129,9208,9306,9394,9478,9616,9694,9770,9909,10003,10083,10139,10193,10259,10332,10410,10481,10565,10638,10716,10789,10864,10974,11064,11139,11233,11331,11405,11482,11582,11635,11719,11787,11876,11965,12027,12092,12155,12225,12332,12432,12532,12628,12688,12746,12826,13322,13397,13478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f64dc84a821f94336e410d15a520d5cf\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,13155", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,13232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3cd7b7d97291c621d53ca066c44c69e\\transformed\\play-services-basement-18.2.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5570", "endColumns": "142", "endOffsets": "5708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e7eb301ebab3935e1c8a7c29a974d3ae\\transformed\\react-android-0.79.4-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,278,368,436,504,581,662,746,826,898,986,1073,1152,1233,1313,1390,1468,1542,1626,1700,1780,1851", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "125,197,273,363,431,499,576,657,741,821,893,981,1068,1147,1228,1308,1385,1463,1537,1621,1695,1775,1846,1929"}, "to": {"startLines": "33,74,76,77,79,93,94,95,142,143,144,145,150,151,152,153,154,155,156,157,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3026,7431,7572,7648,7801,8820,8888,8965,12831,12915,12995,13067,13483,13570,13649,13730,13810,13887,13965,14039,14224,14298,14378,14449", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "3096,7498,7643,7733,7864,8883,8960,9041,12910,12990,13062,13150,13565,13644,13725,13805,13882,13960,14034,14118,14293,14373,14444,14527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\97ed3adf425b530d685cdd8470e3d9b5\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "39,40,41,42,43,44,45,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3524,3620,3722,3821,3920,4026,4130,14123", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3615,3717,3816,3915,4021,4125,4243,14219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d29e10314c2a4b4c7355c13794550e06\\transformed\\play-services-base-18.0.1\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4560,4665,4814,4942,5052,5206,5340,5462,5713,5886,5994,6149,6277,6438,6577,6643,6704", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "4660,4809,4937,5047,5201,5335,5457,5565,5881,5989,6144,6272,6433,6572,6638,6699,6775"}}]}]}