<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#202121</color>
    <color name="iconBackground">#202121</color>
    <color name="splashscreen_background">#202121</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">mobile-app</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="expo_system_ui_user_interface_style" translatable="false">automatic</string>
    <string name="facebook_app_id">651727944558014</string>
    <string name="facebook_client_token">********************************</string>
    <string name="fb_login_protocol_scheme">fb651727944558014</string>
    <style name="AppTheme" parent="Theme.EdgeToEdge">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#202121</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/ic_launcher_background</item>
  </style>
</resources>