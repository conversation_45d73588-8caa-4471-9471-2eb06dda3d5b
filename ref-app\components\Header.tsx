import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  leftComponent?: React.ReactNode;
  rightComponent?: React.ReactNode;
  onBackPress?: () => void;
}

const { width } = Dimensions.get('window');
const FONT_SCALE = width / 400;

const Header: React.FC<HeaderProps> = ({ title, showBackButton, leftComponent, rightComponent, onBackPress }) => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);
  
  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={[styles.safeArea, { paddingTop: insets.top }]}>
      <View style={styles.headerBar}>

        {/* --- Left Column --- */}
        <View style={[styles.sideContainer, styles.leftContainer]}>
          {leftComponent ? (
            leftComponent
          ) : showBackButton ? (
            <TouchableOpacity style={styles.button} onPress={handleBackPress}>
              <MaterialIcons name="arrow-back-ios" size={22 * FONT_SCALE} color={theme.primary} style={{ marginLeft: 8 }} />
            </TouchableOpacity>
          ) : null}
        </View>

        {/* --- Center Column --- */}
        <View style={styles.titleContainer}>
          <Text style={[styles.headerTitle, { fontSize: 17 * FONT_SCALE }]} numberOfLines={1}>
            {title}
          </Text>
        </View>

        {/* --- Right Column --- */}
        <View style={[styles.sideContainer, styles.rightContainer]}>
          {rightComponent}
        </View>

      </View>
    </View>
  );
};

// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  safeArea: {
    // This wrapper takes the main screen background color, so the status bar area blends in.
    backgroundColor: '#FFFFFF',
  },
  headerBar: {
    // The actual header bar gets the themed card color.
    backgroundColor: theme.background,
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.background,
  },
  sideContainer: {
    flex: 1, 
    justifyContent: 'center',
  },
  leftContainer: {
    alignItems: 'flex-start',
  },
  rightContainer: {
    alignItems: 'flex-end',
  },
  titleContainer: {
    flex: 2, 
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  button: {
    padding: 5,
  },
  headerTitle: {
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
  },
});

export default Header;