import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // Enable validation
  app.useGlobalPipes(new ValidationPipe());

  console.log('🚀 API starting on port 3000...');
  await app.listen(process.env.PORT ?? 3000);
  console.log('✅ API is running on port 3000');
}
void bootstrap();
