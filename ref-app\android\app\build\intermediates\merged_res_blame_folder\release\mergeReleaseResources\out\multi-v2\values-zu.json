{"logs": [{"outputFile": "com.refrate.refereetracker.app-mergeReleaseResources-54:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f64dc84a821f94336e410d15a520d5cf\\transformed\\appcompat-1.7.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,536,648,736,839,954,1033,1110,1201,1294,1389,1483,1583,1676,1771,1865,1956,2049,2130,2234,2337,2435,2542,2649,2754,2911,12401", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "424,531,643,731,834,949,1028,1105,1196,1289,1384,1478,1578,1671,1766,1860,1951,2044,2125,2229,2332,2430,2537,2644,2749,2906,3002,12478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\97ed3adf425b530d685cdd8470e3d9b5\\transformed\\core-1.13.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3421,3519,3623,3722,3825,3931,4038,12725", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "3514,3618,3717,3820,3926,4033,4146,12821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9a19d447f99dacc987e9045d6bd5cdca\\transformed\\browser-1.6.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6755,7108,7216,7328", "endColumns": "111,107,111,111", "endOffsets": "6862,7211,7323,7435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3cd7b7d97291c621d53ca066c44c69e\\transformed\\play-services-basement-18.2.0\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5527", "endColumns": "131", "endOffsets": "5654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d29e10314c2a4b4c7355c13794550e06\\transformed\\play-services-base-18.0.1\\res\\values-zu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4470,4581,4757,4892,4996,5164,5293,5417,5659,5819,5929,6094,6225,6383,6540,6601,6670", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "4576,4752,4887,4991,5159,5288,5412,5522,5814,5924,6089,6220,6378,6535,6596,6665,6750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6550f91160cccf4761958ba318c6d009\\transformed\\material-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1075,1142,1245,1320,1383,1475,1546,1611,1678,1750,1822,1876,1997,2056,2120,2174,2251,2383,2468,2545,2635,2715,2796,2945,3032,3115,3257,3349,3427,3483,3541,3607,3679,3756,3827,3910,3990,4069,4144,4223,4327,4417,4490,4584,4681,4755,4828,4927,4982,5066,5134,5222,5311,5373,5437,5500,5571,5680,5791,5894,6002,6062,6124,6206,6289,6365", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "266,344,421,498,592,680,792,918,999,1070,1137,1240,1315,1378,1470,1541,1606,1673,1745,1817,1871,1992,2051,2115,2169,2246,2378,2463,2540,2630,2710,2791,2940,3027,3110,3252,3344,3422,3478,3536,3602,3674,3751,3822,3905,3985,4064,4139,4218,4322,4412,4485,4579,4676,4750,4823,4922,4977,5061,5129,5217,5306,5368,5432,5495,5566,5675,5786,5889,5997,6057,6119,6201,6284,6360,6443"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3162,3239,3333,4151,4263,4389,6867,6938,7005,7440,7515,7578,7670,7741,7806,7873,7945,8017,8071,8192,8251,8315,8369,8446,8578,8663,8740,8830,8910,8991,9140,9227,9310,9452,9544,9622,9678,9736,9802,9874,9951,10022,10105,10185,10264,10339,10418,10522,10612,10685,10779,10876,10950,11023,11122,11177,11261,11329,11417,11506,11568,11632,11695,11766,11875,11986,12089,12197,12257,12319,12483,12566,12642", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "316,3080,3157,3234,3328,3416,4258,4384,4465,6933,7000,7103,7510,7573,7665,7736,7801,7868,7940,8012,8066,8187,8246,8310,8364,8441,8573,8658,8735,8825,8905,8986,9135,9222,9305,9447,9539,9617,9673,9731,9797,9869,9946,10017,10100,10180,10259,10334,10413,10517,10607,10680,10774,10871,10945,11018,11117,11172,11256,11324,11412,11501,11563,11627,11690,11761,11870,11981,12084,12192,12252,12314,12396,12561,12637,12720"}}]}]}