import React, { useState } from 'react';
import {
    View, Text, ScrollView, TouchableOpacity, StyleSheet, TextInput, Modal, FlatList, SafeAreaView
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Header from '../components/Header';
import { countries, countryFas } from './data'; // Assumes data.ts is in the same folder
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct

// --- Type Definitions for Component Props ---
interface ReadOnlyRowProps { label: string; value: string; styles: any; }
interface NavigationRowProps { label: string; isDestructive?: boolean; onPress?: () => void; styles: any; theme: any; }
interface SelectionDisplayRowProps { label: string; value: string; onPress: () => void; isDestructive?: boolean; styles: any; theme: any; }
interface InputRowProps {
    label: string;
    value: string;
    onValueChange: (text: string) => void;
    placeholder?: string;
    autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
    styles: any;
}
interface MeasurementRowProps {
    label: string;
    value: string;
    onValueChange: (text: string) => void;
    unit: string;
    onUnitChange: () => void;
    styles: any;
    theme: any;
}
interface SelectionRowProps { label: string; value: string; onPress: () => void; styles: any; theme: any; }
interface SelectionModalProps {
    visible: boolean;
    title: string;
    data: readonly string[];
    onSelect: (item: string) => void;
    onClose: () => void;
    styles: any;
}
interface SectionProps { title: string; children: React.ReactNode; styles: any; }


// --- Reusable Components ---
const Section: React.FC<SectionProps> = ({ title, children, styles }) => (
  <>
    <Text style={styles.sectionHeader}>{title}</Text>
    <View style={styles.sectionBox}>{children}</View>
  </>
);

const ReadOnlyRow: React.FC<ReadOnlyRowProps> = ({ label, value, styles }) => (
    <View style={styles.row}><Text style={styles.rowLabel}>{label}</Text><Text style={styles.rowValue}>{value}</Text></View>
);

const NavigationRow: React.FC<NavigationRowProps> = ({ label, isDestructive = false, onPress = () => {}, styles, theme }) => (
    <TouchableOpacity style={styles.row} activeOpacity={0.7} onPress={onPress}>
        <Text style={[styles.rowLabel, isDestructive && styles.destructiveText]}>{label}</Text>
        <MaterialIcons name="chevron-right" size={22} color={isDestructive ? '#d32f2f' : theme.text} style={{ opacity: isDestructive ? 1 : 0.5 }} />
    </TouchableOpacity>
);

const SelectionDisplayRow: React.FC<SelectionDisplayRowProps> = ({ label, value, onPress, isDestructive = false, styles, theme }) => (
    <TouchableOpacity style={styles.row} activeOpacity={0.7} onPress={onPress}>
        <Text style={[styles.rowLabel, isDestructive && styles.destructiveText]}>{label}</Text>
        <View style={styles.rowRight}><Text style={styles.rowValue}>{value}</Text><MaterialIcons name="chevron-right" size={22} color={isDestructive ? '#d32f2f' : theme.text} style={{ opacity: isDestructive ? 1 : 0.5 }}/></View>
    </TouchableOpacity>
);

const InputRow: React.FC<InputRowProps> = ({ label, value, onValueChange, placeholder, autoCapitalize = 'words', styles }) => (
    <View style={styles.row}>
        <Text style={styles.rowLabel}>{label}</Text>
        <TextInput style={styles.textInput} value={value} onChangeText={onValueChange} placeholder={placeholder || 'Enter text'} placeholderTextColor="#c7c7cd" autoCapitalize={autoCapitalize}/>
    </View>
);

const MeasurementRow: React.FC<MeasurementRowProps> = ({ label, value, onValueChange, unit, onUnitChange, styles, theme }) => (
    <View style={styles.row}>
        <Text style={styles.rowLabel}>{label}</Text>
        <View style={styles.rowRight}>
            <TextInput style={styles.measurementInput} value={value} onChangeText={onValueChange} keyboardType="numeric" placeholder="0" placeholderTextColor="#c7c7cd" />
            <TouchableOpacity style={styles.unitButton} onPress={onUnitChange}>
                <Text style={styles.unitText}>{unit}</Text>
                <MaterialIcons name="unfold-more" size={20} color={theme.text} style={{ opacity: 0.7 }}/>
            </TouchableOpacity>
        </View>
    </View>
);

const SelectionRow: React.FC<SelectionRowProps> = ({ label, value, onPress, styles, theme }) => (
    <TouchableOpacity style={styles.row} activeOpacity={0.7} onPress={onPress}>
        <Text style={styles.rowLabel}>{label}</Text>
        <View style={styles.rowRight}>
            <Text style={styles.rowValue}>{value}</Text>
            <MaterialIcons name="unfold-more" size={20} color={theme.text} style={{ opacity: 0.7 }}/>
        </View>
    </TouchableOpacity>
);

const SelectionModal: React.FC<SelectionModalProps> = ({ visible, title, data, onSelect, onClose, styles }) => (
    <Modal animationType="slide" transparent={false} visible={visible} onRequestClose={onClose}>
        <SafeAreaView style={styles.modalContainer}>
            <View style={styles.modalHeader}><Text style={styles.modalTitle}>{title}</Text><TouchableOpacity onPress={onClose}><Text style={styles.modalCloseButton}>Done</Text></TouchableOpacity></View>
            <FlatList data={data} keyExtractor={(item) => item} renderItem={({ item }) => (
                <TouchableOpacity style={styles.modalItem} onPress={() => onSelect(item)}><Text style={styles.modalItemText}>{item}</Text></TouchableOpacity>
            )}/>
        </SafeAreaView>
    </Modal>
);

// --- Main Account Screen Component ---
const Account = () => {
    const { theme, isDarkMode } = useTheme();
    const styles = getStyles(theme, isDarkMode);

    const [firstName, setFirstName] = useState('Dave');
    const [lastName, setLastName] = useState('John');
    const [username, setUsername] = useState('davejohn');
    const [refereeLevel, setRefereeLevel] = useState('National Level 1');
    const [weight, setWeight] = useState('');
    const [weightUnit, setWeightUnit] = useState('Kg');
    const weightUnits = ['Kg', 'Lb', 'St'];
    const [height, setHeight] = useState('');
    const [heightUnit, setHeightUnit] = useState('cm');
    const heightUnits = ['cm', 'm', 'ft', 'in'];
    const [gender, setGender] = useState('Male');
    const genderOptions = ['Male', 'Female'];
    const [selectedCountry, setSelectedCountry] = useState('United Kingdom');
    const [selectedFa, setSelectedFa] = useState('');
    const [isCountryModalVisible, setCountryModalVisible] = useState(false);
    const [isFaModalVisible, setFaModalVisible] = useState(false);

    const cycleUnit = (currentUnit: string, units: string[], setUnit: React.Dispatch<React.SetStateAction<string>>) => {
        const currentIndex = units.indexOf(currentUnit);
        const nextIndex = (currentIndex + 1) % units.length;
        setUnit(units[nextIndex]);
    };

    const handleSelectCountry = (country: string) => {
        setSelectedCountry(country);
        setSelectedFa('');
        setCountryModalVisible(false);
    };

    const handleSelectFa = (fa: string) => {
        setSelectedFa(fa);
        setFaModalVisible(false);
    };

    const availableFas = countryFas[selectedCountry];
    const securityItems = [ { label: '2FA' }, { label: 'Subscription' }, { label: 'Biometry' }, { label: 'Change password' }];

    return (
        <View style={styles.container}>
            <Header title="Account" showBackButton={true} />
            <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
                <View style={styles.avatarSection}>
                    <View style={styles.avatarCircle}><MaterialIcons name="photo-camera" size={40} color={theme.text} style={{ opacity: 0.5 }} /></View>
                    <Text style={styles.addPicText}>Add Picture</Text>
                </View>

                <Section title="Personal Info" styles={styles}>
                    <InputRow label="First Name" value={firstName} onValueChange={setFirstName} placeholder="Your first name" styles={styles} />
                    <InputRow label="Last Name" value={lastName} onValueChange={setLastName} placeholder="Your last name" styles={styles} />
                    <MeasurementRow label="Weight" value={weight} onValueChange={setWeight} unit={weightUnit} onUnitChange={() => cycleUnit(weightUnit, weightUnits, setWeightUnit)} styles={styles} theme={theme} />
                    <MeasurementRow label="Height" value={height} onValueChange={setHeight} unit={heightUnit} onUnitChange={() => cycleUnit(heightUnit, heightUnits, setHeightUnit)} styles={styles} theme={theme} />
                    <SelectionRow label="Gender" value={gender} onPress={() => cycleUnit(gender, genderOptions, setGender)} styles={styles} theme={theme} />
                    <InputRow label="Username" value={username} onValueChange={setUsername} autoCapitalize="none" placeholder="Your username" styles={styles} />
                    <NavigationRow label="Change Email address" styles={styles} theme={theme} />
                </Section>
                
                <Section title="Referee Details" styles={styles}>
                    <SelectionDisplayRow label="Country" value={selectedCountry || 'Select'} onPress={() => setCountryModalVisible(true)} styles={styles} theme={theme} />
                    {availableFas && (<SelectionDisplayRow label="Country FA" value={selectedFa || 'Select'} onPress={() => setFaModalVisible(true)} styles={styles} theme={theme} />)}
                    <InputRow label="Referee Level" value={refereeLevel} onValueChange={setRefereeLevel} placeholder="e.g., National Level 1" styles={styles} />
                </Section>

                <Section title="Security" styles={styles}>
                    {securityItems.map(item => <NavigationRow key={item.label} label={item.label} styles={styles} theme={theme}/>)}
                    <ReadOnlyRow label="App Version" value="V 1.2.0" styles={styles} />
                </Section>
                
                <View style={[styles.sectionBox, { marginTop: 20 }]}>
                    <NavigationRow label="Delete Account" isDestructive={true} styles={styles} theme={theme} />
                </View>
            </ScrollView>
            
            <SelectionModal visible={isCountryModalVisible} title="Select a Country" data={countries} onSelect={handleSelectCountry} onClose={() => setCountryModalVisible(false)} styles={styles} />
            {availableFas && (<SelectionModal visible={isFaModalVisible} title="Select a Country FA" data={availableFas} onSelect={handleSelectFa} onClose={() => setFaModalVisible(false)} styles={styles} />)}
        </View>
    );
};

// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.background },
    scrollContent: { paddingHorizontal: 16, paddingBottom: 32 },
    avatarSection: { alignItems: 'center', marginTop: 8, marginBottom: 18 },
    avatarCircle: { width: 100, height: 100, borderRadius: 50, backgroundColor: theme.border, alignItems: 'center', justifyContent: 'center', marginBottom: 8 },
    addPicText: { fontWeight: '600', fontSize: 15, color: theme.text },
    sectionHeader: { fontWeight: '700', fontSize: 15, color: theme.text, marginTop: 10, marginBottom: 12, marginLeft: 4 },
    sectionBox: { 
        backgroundColor: theme.card, 
        borderRadius: 12, 
        overflow: 'hidden', 
        marginBottom: 18,
        borderWidth: 1,
        borderColor: theme.border1,
        elevation: isDarkMode ? 0 : 2 
    },
    row: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingVertical: 14, paddingHorizontal: 16, backgroundColor: 'transparent', borderBottomWidth: 1, borderBottomColor: theme.border1 },
    rowLabel: { fontSize: 15, color: theme.text, fontWeight: '500' },
    rowRight: { flexDirection: 'row', alignItems: 'center' },
    rowValue: { fontSize: 15, color: theme.text, opacity: 0.7, fontWeight: '500', marginRight: 8 },
    destructiveText: { color: '#d32f2f' },
    textInput: { flex: 1, fontSize: 15, color: theme.text, fontWeight: '500', textAlign: 'right' },
    measurementInput: { fontSize: 15, color: theme.text, fontWeight: '500', textAlign: 'right', marginRight: 10, minWidth: 50 },
    unitButton: { flexDirection: 'row', alignItems: 'center', paddingVertical: 4, paddingHorizontal: 8, borderRadius: 6, backgroundColor: theme.border },
    unitText: { fontSize: 15, color: theme.text, fontWeight: '500', marginRight: 4 },
    modalContainer: { flex: 1, backgroundColor: theme.background },
    modalHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: theme.border },
    modalTitle: { fontSize: 20, fontWeight: '700', color: theme.text },
    modalCloseButton: { fontSize: 17, color: '#068B47', fontWeight: '600' }, // Standard iOS blue
    modalItem: { paddingVertical: 16, paddingHorizontal: 20, borderBottomWidth: 1, borderBottomColor: theme.border },
    modalItemText: { fontSize: 16, color: theme.text },
});

export default Account;