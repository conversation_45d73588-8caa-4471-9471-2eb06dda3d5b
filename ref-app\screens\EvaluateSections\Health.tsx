import React from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { LineChart } from 'react-native-gifted-charts';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../contexts/ThemeContext'; // Ensure this path is correct

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// --- Types ---
interface HeartRateData { value: number; label: string; }
interface ZoneData { value: number; labelValue: string; label: string; color: string; }
interface IntensityMatch { time: string; teams: string; date: string; }
interface StackedBarMatchItem { zone1: number; zone2: number; zone3: number; zone4: number; zone5: number; labels: string[]; }
interface ZoneGridItem { zone: string; range: string; color: string; }
interface StatCardProps { title: string; value: number; goal: number; unit: string; iconName: string; color: string; styles: any; theme: any; }

// --- Static Colors (Don't change with theme) ---
const AppColors = {
  maxHr: '#ff0033',
  avgHr: '#FFB304',
  zone1: '#068B47',
  zone2: '#fde24b',
  zone3: '#FFB304',
  zone4: '#FF6F00',
  zone5: '#ff0033',
};

// --- Reusable Components (Now accept styles & theme) ---
const StatCard: React.FC<StatCardProps> = ({ title, value, goal, unit, iconName, color, styles, theme }) => {
  const percentage = Math.min((value / goal) * 100, 100);
  const formattedValue = value.toLocaleString('en-US');
  return (
    <View style={styles.card}>
      <View style={styles.statContent}>
        <View style={styles.statLeftColumn}>
          <Text style={styles.statTitle}>{title}</Text>
          <View style={styles.valueRow}>
            <Icon name={iconName} size={32} color={color} style={styles.statIcon} />
            <View style={styles.valueAndGoalContainer}>
              <Text style={styles.statValueText}>{formattedValue}<Text style={styles.statUnitText}>{unit}</Text></Text>
              <Text style={styles.statGoalText}>Goal: {goal.toLocaleString('en-US')}{unit.includes('Kcal') ? ' Kcal' : ''}</Text>
            </View>
          </View>
        </View>
        <View style={styles.statProgress}>
          <AnimatedCircularProgress size={80} width={8} fill={percentage} tintColor={color} backgroundColor={theme.border} rotation={0} lineCap="round">
            {(fill: number) => <Text style={styles.progressText}>{Math.round(fill)}%</Text>}
          </AnimatedCircularProgress>
        </View>
      </View>
    </View>
  );
};

const HeartRateStatsCard: React.FC<{ styles: any; theme: any; }> = ({ styles, theme }) => (
    <View style={styles.card}>
        <View style={styles.centeredHeaderContainer}><Text style={styles.cardTitle}>Heart Rate</Text></View>
        <View style={styles.statsRow}>
            <View style={styles.statBox}><Text style={[styles.largeStatText, { color: theme.primary }]}>177 <Text style={styles.bpmText}>bpm</Text></Text><Text style={styles.statLabel}>Max HR</Text></View>
            <View style={styles.statBox}><Text style={[styles.largeStatText, { color: theme.primary }]}>152 <Text style={styles.bpmText}>bpm</Text></Text><Text style={styles.statLabel}>Average HR</Text></View>
        </View>
    </View>
);

const HeartRateChartCard: React.FC<{maxHrData: HeartRateData[]; avgHrData: HeartRateData[]; styles: any; theme: any;}> = ({ maxHrData, avgHrData, styles, theme }) => {
    const count = maxHrData.length;
    const chartWidthBase = SCREEN_WIDTH - 64;
    const availableWidth = chartWidthBase - 60;
    const spacing = Math.max(25, Math.min(70, availableWidth / count));
    const initSpacing = Math.max(15, spacing * 0.4) + 15;
    const renderLabel = (label: string) => {
        const [l, r] = label.split(' - ');
        return (
            <View style={styles.xAxisLabelWrapper}>
                <Text style={styles.xAxisLabel} numberOfLines={1} adjustsFontSizeToFit minimumFontScale={0.7}>{l}</Text>
                <Text style={styles.xAxisLabelDash}>-</Text>
                <Text style={styles.xAxisLabel} numberOfLines={1} adjustsFontSizeToFit minimumFontScale={0.7}>{r}</Text>
            </View>
        );
    };
    const maxData = maxHrData.map(i => ({...i, labelComponent: () => renderLabel(i.label)}));
    const avgData = avgHrData.map(i => ({...i, labelComponent: () => renderLabel(i.label)}));
    return (
        <View style={[styles.card, { padding: 16 }]}>
            <View style={styles.centeredHeaderContainer}><Text style={styles.cardSubtitle}>Average across matches</Text></View>
            <View style={{ marginTop: 20 }}>
                <LineChart data={maxData} data2={avgData} height={200} width={initSpacing + (count - 1) * spacing + 15} color={AppColors.maxHr} color2={AppColors.avgHr} dataPointsColor={AppColors.maxHr} dataPointsColor2={AppColors.avgHr} dataPointsRadius={SCREEN_WIDTH < 350 ? 3 : 4} thickness={2} yAxisLabelTexts={['60', '80', '100', '120', '140', '160', '180', '200']} yAxisTextStyle={styles.axisText} yAxisColor={theme.percentborder} rulesColor={theme.percentborder} noOfSections={7} maxValue={200} xAxisColor={theme.percentborder} xAxisLabelTextStyle={styles.axisText} initialSpacing={initSpacing} spacing={spacing} />
            </View>
            <View style={styles.legendContainer}><View style={styles.legendItem}><View style={[styles.legendColor, { backgroundColor: AppColors.maxHr }]} /><Text style={styles.legendText}>Max Heart Rate</Text></View><View style={styles.legendItem}><View style={[styles.legendColor, { backgroundColor: AppColors.avgHr }]} /><Text style={styles.legendText}>Average Heart Rate</Text></View></View>
        </View>
    );
};

const LastMatchesChartCard: React.FC<{ data: StackedBarMatchItem[]; styles: any; }> = ({ data, styles }) => {
    const Y_AXIS_MAX = 120;
    const yAxisLabels = ['120', '100', '80', '60', '40', '20', '0'];
    return (
        <View style={styles.card}>
            <View style={styles.centeredHeaderContainer}><Text style={styles.cardTitle}>Last 5 Matches</Text></View>
            <View style={styles.stackedChartBody}>
                <View style={styles.yAxisContainer}><View style={styles.yAxisLabelWrapper}><View style={styles.rotatedLabelContainer}><Text style={styles.yAxisLabelText}>Time spent in zone / mins</Text></View></View><View style={styles.yAxisLabels}>{yAxisLabels.map((label) => (<Text key={label} style={styles.yAxisTickText}>{label}</Text>))}</View></View>
                <View style={styles.mainChartArea}>
                    <View style={styles.barsArea}>{data.map((match, index) => {
                        const totalTime = match.zone1 + match.zone2 + match.zone3 + match.zone4 + match.zone5;
                        if (totalTime === 0) return <View key={index} style={styles.barWrapper} />;
                        const barHeightPercentage = (totalTime / Y_AXIS_MAX) * 100;
                        return (<View key={index} style={styles.barWrapper}><View style={[styles.barStack, { height: `${Math.min(barHeightPercentage, 100)}%` }]}><View style={{height: `${(match.zone5 / totalTime) * 100}%`, backgroundColor: AppColors.zone5}} /><View style={{height: `${(match.zone4 / totalTime) * 100}%`, backgroundColor: AppColors.zone4}} /><View style={{height: `${(match.zone3 / totalTime) * 100}%`, backgroundColor: AppColors.zone3}} /><View style={{height: `${(match.zone2 / totalTime) * 100}%`, backgroundColor: AppColors.zone2}} /><View style={{height: `${(match.zone1 / totalTime) * 100}%`, backgroundColor: AppColors.zone1}} /></View></View>);
                    })}</View>
                    <View style={styles.xAxisLabelsArea}>{data.map((match, i) => (<View key={i} style={styles.xAxisLabelWrapperStacked}><Text style={styles.xAxisLabelStacked} numberOfLines={1} adjustsFontSizeToFit minimumFontScale={0.7}>{match.labels[0]}</Text><Text style={styles.xAxisLabelDashStacked}>-</Text><Text style={styles.xAxisLabelStacked} numberOfLines={1} adjustsFontSizeToFit minimumFontScale={0.7}>{match.labels[1]}</Text></View>))}</View>
                </View>
            </View>
        </View>
    );
};

const IntensityMatchesCard: React.FC<{ matches: IntensityMatch[]; styles: any; theme: any; }> = ({ matches, styles, theme }) => {
    return (
        <View style={styles.card}>
            <View style={styles.centeredHeaderContainer}>
                <Text style={[styles.cardTitle, { textAlign: 'center' }]}>Matches with highest intensity workout</Text>
                <Text style={styles.cardSubtitle}>(Z4 + Z5)</Text>
            </View>
            <View style={{ marginTop: 16 }}>
                {matches.map((match, index) => {
                    const teams = match.teams.split(/ vs /i); // Split case-insensitively
                    const team1 = teams[0];
                    const team2 = teams.length > 1 ? teams[1] : '';

                    return (
                        <View
                            key={index}
                            style={[
                                styles.intensityMatchRow,
                                // Remove border and some padding for the last item for a cleaner look
                                index === matches.length - 1 && { borderBottomWidth: 0, paddingBottom: 0, marginBottom: 0 }
                            ]}
                        >
                            <View style={styles.intensityTimeContainer}>
                                <Text style={[styles.intensityTimeText, { color: theme.primary }]}>{match.time}</Text>
                            </View>
                            <View style={styles.intensityDetailsContainer}>
                                <Text style={styles.intensityTeamsText}>
                                    {team1}
                                    {team2 ? <Text style={{ color: theme.primary }}> vs </Text> : ''}
                                    {team2}
                                </Text>
                            </View>
                            <View style={styles.intensityDateContainer}>
                                <Text style={styles.intensityDateText}>{match.date}</Text>
                            </View>
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

const ZoneInfoCard: React.FC<{zoneData: ZoneData[]; zoneGridData: ZoneGridItem[]; styles: any;}> = ({ zoneData, zoneGridData, styles }) => (
    <View style={styles.card}>
        <View style={styles.centeredHeaderContainer}>
            <Text style={styles.cardTitle}>Heart Rate Zones</Text>
            <Text style={styles.cardSubtitle}>Time spent in heart rate zones</Text>
        </View>
        <View style={styles.zoneChartContainer}>
            {zoneData.map((item: ZoneData) => (
                <View key={item.label} style={styles.zoneBarWrapper}>
                    <View style={[styles.zoneLabelTop, { backgroundColor: item.color }]}><Text style={[styles.zoneLabelText, { fontSize: SCREEN_WIDTH < 350 ? 11 : 13 }]} numberOfLines={1} adjustsFontSizeToFit>{item.labelValue}</Text></View>
                    <View style={styles.zoneBarContainer}><View style={[styles.zoneBar, { height: `${item.value}%`, backgroundColor: item.color }]} /></View>
                    <Text style={styles.zoneLabelBottom}>{item.label}</Text>
                </View>
            ))}
        </View>
        <View style={styles.sectionDivider} />
        <View style={styles.zoneGridContainer}>
            {zoneGridData.map((item, index) => (
                <View key={item.zone} style={[styles.zoneGridColumn, index === 0 && { borderLeftWidth: 0 }]}>
                    <View style={styles.zoneGridHeader}><Text style={styles.zoneGridHeaderText}>{item.zone}</Text></View>
                    <View style={[styles.zoneGridBody, { backgroundColor: item.color }]}><Text style={styles.zoneGridBodyText}>{item.range}</Text></View>
                </View>
            ))}
        </View>
    </View>
);

// --- Health Component ---
const Health: React.FC = () => {
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);

  const maxHrDataAll: HeartRateData[] = [ { value: 185, label: 'AFC - MA' }, { value: 178, label: 'CAL - ABE' }, { value: 188, label: 'MON - SI' }, { value: 180, label: 'PIL - NEW' }, { value: 178, label: 'SUD - CAL' }];
  const avgHrDataAll: HeartRateData[] = [ { value: 155, label: 'AFC - MA' }, { value: 142, label: 'CAL - ABE' }, { value: 172, label: 'MON - SI' }, { value: 148, label: 'PIL - NEW' }, { value: 150, label: 'SUD - CAL' }];
  const lastMatchesDataAll: StackedBarMatchItem[] = [ { zone1: 5, zone2: 5, zone3: 80, zone4: 3, zone5: 2, labels: ['AFC', 'MA'] }, { zone1: 10, zone2: 10, zone3: 45, zone4: 20, zone5: 10, labels: ['CAL', 'ABE'] }, { zone1: 15, zone2: 15, zone3: 10, zone4: 40, zone5: 40, labels: ['MON', 'SI'] }, { zone1: 5, zone2: 5, zone3: 35, zone4: 40, zone5: 40, labels: ['PIL', 'NEW'] }, { zone1: 5, zone2: 5, zone3: 25, zone4: 35, zone5: 35, labels: ['SUD', 'CAL'] }];
  const last5MaxHrData = maxHrDataAll.slice(0, 5);
  const last5AvgHrData = avgHrDataAll.slice(0, 5);
  const last5MatchesData = lastMatchesDataAll.slice(0, 5);
  const zoneData: ZoneData[] = [ { value: 15, labelValue: '00:41', label: '1', color: AppColors.zone1 }, { value: 30, labelValue: '05:30', label: '2', color: AppColors.zone2 }, { value: 70, labelValue: '25:01', label: '3', color: AppColors.zone3 }, { value: 95, labelValue: '39:34', label: '4', color: AppColors.zone4 }, { value: 45, labelValue: '11:60', label: '5', color: AppColors.zone5 } ];
  const intensityMatchesData: IntensityMatch[] = [ { time: '90:07', teams: 'The Oak FC vs Crickhowell FC', date: '11/11/2023' }, { time: '86:21', teams: 'Chepstow Town FC 3rd team vs Caldicot Castle FC Reserves', date: '09/02/2023' }, { time: '86:17', teams: 'Monmouth Town vs Sifil afc', date: '03/08/2025' }];
  const zoneGridData: ZoneGridItem[] = [ { zone: '1', range: '50-\n59%', color: AppColors.zone1 }, { zone: '2', range: '60-\n69%', color: AppColors.zone2 }, { zone: '3', range: '70-\n79%', color: AppColors.zone3 }, { zone: '4', range: '80-\n89%', color: AppColors.zone4 }, { zone: '5', range: '> 90%', color: AppColors.zone5 } ];

  return (
    <ScrollView style={styles.screenContainer} showsVerticalScrollIndicator={false}>
      <View style={styles.contentContainer}>
        <HeartRateStatsCard styles={styles} theme={theme} />
        <HeartRateChartCard maxHrData={last5MaxHrData} avgHrData={last5AvgHrData} styles={styles} theme={theme} />
        <ZoneInfoCard zoneData={zoneData} zoneGridData={zoneGridData} styles={styles} />
        <LastMatchesChartCard data={last5MatchesData} styles={styles} />
        <IntensityMatchesCard matches={intensityMatchesData} styles={styles} theme={theme} />
        <StatCard title="Steps" value={600} goal={12000} unit=" Steps" iconName="shoe-sneaker" color={theme.primary} styles={styles} theme={theme} />
        <StatCard title="Calories" value={9800} goal={12000} unit=" Kcal" iconName="fire" color={theme.primary} styles={styles} theme={theme} />
      </View>
    </ScrollView>
  );
};

// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  screenContainer: { flex: 1, backgroundColor: theme.background },
  contentContainer: { padding: 16 },
  card: { 
    backgroundColor: theme.card, 
    borderRadius: 12, 
    padding: 16, 
    marginBottom: 16, 
    borderWidth: 1, 
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 3,
  },
  centeredHeaderContainer: { alignItems: 'center', marginBottom: 8 },
  cardTitle: { fontSize: 16, fontWeight: 'bold', color: theme.text },
  cardSubtitle: { fontSize: 13, color: theme.text, opacity: 0.7, marginTop: 4 },
  statsRow: { flexDirection: 'row', marginTop: 8 },
  statBox: { flex: 1, alignItems: 'center' },
  largeStatText: { fontSize: SCREEN_WIDTH < 350 ? 24 : 28, fontWeight: 'bold' },
  bpmText: { fontSize: SCREEN_WIDTH < 350 ? 18 : 22 },
  statLabel: { fontSize: 13, color: theme.text, opacity: 0.7, marginTop: 4 },
  axisText: { color: theme.text, opacity: 0.7, fontSize: SCREEN_WIDTH < 350 ? 9.5 : 11 },
  xAxisLabelWrapper: { alignItems: 'center', justifyContent: 'flex-start', paddingHorizontal: 1, marginHorizontal: 4, minWidth: 35, marginLeft: -11 },
  xAxisLabel: { color: theme.text, opacity: 0.7, textAlign: 'center', fontWeight: '600', letterSpacing: -0.2, fontSize: SCREEN_WIDTH < 375 ? 9.5 : 10.5, lineHeight: SCREEN_WIDTH < 375 ? 11 : 12, height: 14 },
  xAxisLabelDash: { color: theme.text, opacity: 0.6, textAlign: 'center', fontWeight: '400', fontSize: SCREEN_WIDTH < 375 ? 8 : 9, lineHeight: SCREEN_WIDTH < 375 ? 9 : 10, height: 10 },
  legendContainer: { flexDirection: 'row', justifyContent: 'center', marginTop: 24, gap: SCREEN_WIDTH < 350 ? 16 : 24, },
  legendItem: { flexDirection: 'row', alignItems: 'center' },
  legendColor: { width: 12, height: 12, borderRadius: 2, marginRight: 8 },
  legendText: { fontSize: SCREEN_WIDTH < 350 ? 10 : 11.5, color: theme.text, opacity: 0.7 },
  zoneChartContainer: { flexDirection: 'row', alignItems: 'flex-end', justifyContent: 'space-between', height: 220, marginBottom: 2, marginTop: 16, paddingHorizontal: SCREEN_WIDTH < 350 ? 4 : 8 },
  zoneBarWrapper: { flex: 1, alignItems: 'center', marginHorizontal: 4, marginBottom: 0, maxWidth: 80 },
  zoneLabelTop: { borderRadius: 8, paddingHorizontal: 8, paddingVertical: 4, marginBottom: 9, maxWidth: '100%', flexShrink: 1, alignSelf: 'center' },
  zoneLabelText: { color: 'white', fontWeight: 'bold', textAlign: 'center' },
  zoneBarContainer: { height: 150, justifyContent: 'flex-end', alignItems: 'center', minWidth: SCREEN_WIDTH < 350 ? 25 : 30 },
  zoneBar: { width: 20, borderTopLeftRadius: 8, borderTopRightRadius: 8 },
  zoneLabelBottom: { marginTop: 8, fontSize: SCREEN_WIDTH < 350 ? 11 : 13, color: theme.text, opacity: 0.7, textAlign: 'center' },
  zoneGridContainer: { flexDirection: 'row', borderRadius: 12, borderWidth: 1, borderColor: theme.percentborder, overflow: 'hidden' },
  zoneGridColumn: { flex: 1, borderLeftWidth: 1, borderColor: theme.percentborder },
  zoneGridHeader: { paddingVertical: 8, backgroundColor: 'transparent', alignItems: 'center', justifyContent: 'center', borderBottomWidth: 1, borderColor: theme.border },
  zoneGridHeaderText: { fontSize: 14, fontWeight: '600', color: theme.text },
  zoneGridBody: { paddingVertical: 12, alignItems: 'center', justifyContent: 'center', minHeight: 70 },
  zoneGridBodyText: { fontSize: 14, color: theme.text, textAlign: 'center', lineHeight: 20 },
  sectionDivider: { marginTop: 24, paddingTop: 16, borderTopWidth: 1, borderTopColor: theme.border },
  stackedChartBody: { flexDirection: 'row', height: 280, marginTop: 16 },
  yAxisContainer: { flexDirection: 'row', width: 60 },
  yAxisLabelWrapper: { width: 30, position: 'relative' },
  rotatedLabelContainer: { position: 'absolute', top: 120, left: -95, width: 220, transform: [{ rotate: '-90deg' }] },
  yAxisLabelText: { fontSize: 11, fontWeight: '500', color: theme.text, opacity: 0.7, textAlign: 'center' },
  yAxisLabels: { flex: 1, justifyContent: 'space-between', paddingBottom: 43, paddingRight: 3 },
  yAxisTickText: { fontSize: 11, color: theme.text, opacity: 0.7, fontWeight: '500', textAlign: 'right' },
  mainChartArea: { flex: 1, justifyContent: 'flex-end' },
  barsArea: { flexDirection: 'row', flex: 1, borderBottomWidth: 1.5, borderBottomColor: theme.text, paddingHorizontal: 3, alignItems: 'flex-end' },
  barWrapper: { flex: 1, justifyContent: 'flex-end', alignItems: 'center', marginHorizontal: 4 },
  barStack: { width: '35%', minWidth: 15, flexDirection: 'column-reverse', overflow: 'hidden', borderTopLeftRadius: 5, borderTopRightRadius: 5 },
  xAxisLabelsArea: { flexDirection: 'row', height: 43, paddingTop: 8, paddingHorizontal: 3 },
  xAxisLabelWrapperStacked: { flex: 1, alignItems: 'center', justifyContent: 'flex-start', marginHorizontal: 2, paddingHorizontal: 1 },
  xAxisLabelStacked: { color: theme.text, opacity: 0.9, textAlign: 'center', fontWeight: '600', letterSpacing: -0.2, fontSize: SCREEN_WIDTH < 375 ? 9.5 : 10.5, lineHeight: SCREEN_WIDTH < 375 ? 11 : 12, height: 14 },
  xAxisLabelDashStacked: { color: theme.text, opacity: 0.6, textAlign: 'center', fontWeight: '400', fontSize: SCREEN_WIDTH < 375 ? 8 : 9, lineHeight: SCREEN_WIDTH < 375 ? 9 : 10, height: 10 },
  
  // --- CORRECTED INTENSITY MATCH STYLES ---
  intensityMatchRow: {
    flexDirection: 'row',
    alignItems: 'flex-start', // Align all items to the top of the row
    paddingVertical: 12,
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  intensityTimeContainer: {
    width: 65, // Fixed width for the time column
    marginRight: 12, // Space between time and team details
  },
  intensityTimeText: {
    fontSize: 16,
    fontWeight: 'bold',
    lineHeight: 20, // Match the line height of other text for vertical alignment
  },
  intensityDetailsContainer: {
    flex: 1, // This allows the team names to take up the available space and wrap
  },
  intensityTeamsText: {
    fontSize: 14,
    color: theme.text,
    lineHeight: 20, // Consistent line height is crucial for wrapped text
  },
  intensityDateContainer: {
    marginLeft: 12, // Space between team details and the date
  },
  intensityDateText: {
    fontSize: 13,
    color: theme.text,
    opacity: 0.7,
    lineHeight: 20, // Match the line height for alignment
  },
  // --- End of corrected styles ---

  statContent: { flexDirection: 'row', alignItems: 'center' },
  statLeftColumn: { flex: 1, flexDirection: 'column', justifyContent: 'center', paddingRight: 8 },
  statTitle: { fontSize: 15, fontWeight: '600', color: theme.text, marginBottom: 10 },
  valueRow: { flexDirection: 'row', alignItems: 'center' },
  statIcon: { marginRight: 12 },
  valueAndGoalContainer: { flexDirection: 'column' },
  statValueText: { fontSize: 22, fontWeight: 'bold', color: theme.text },
  statUnitText: { fontSize: 16, fontWeight: 'normal', color: theme.text, opacity: 0.7 },
  statGoalText: { fontSize: 13, color: theme.text, opacity: 0.7, marginTop: 4 },
  statProgress: {},
  progressText: { fontSize: 18, fontWeight: 'bold', color: theme.text },
});

export default Health;