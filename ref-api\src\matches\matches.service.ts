import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { CreateMatchDto } from './dto/create-match.dto';
import { UpdateMatchDto } from './dto/update-match.dto';
import { Match, MatchStatus } from '@prisma/client';

@Injectable()
export class MatchesService {
  constructor(private prisma: PrismaService) {}

  async create(createMatchDto: CreateMatchDto, refereeId: string): Promise<Match> {
    try {
      // If using default referee ID, find the default referee from seed
      let actualRefereeId = refereeId;

      if (refereeId === 'default-referee-id') {
        const defaultReferee = await this.prisma.user.findFirst({
          where: { email: '<EMAIL>' }
        });

        if (defaultReferee) {
          actualRefereeId = defaultReferee.id;
        } else {
          throw new BadRequestException('Default referee not found. Please run database seed.');
        }
      }

      // Validate referee exists
      const referee = await this.prisma.user.findUnique({
        where: { id: actualRefereeId }
      });

      if (!referee) {
        throw new BadRequestException('Referee not found');
      }

      // Create the match
      const match = await this.prisma.match.create({
        data: {
          competition: createMatchDto.competition,
          venue: createMatchDto.venue,
          matchDate: new Date(createMatchDto.matchDate),
          officialRole: createMatchDto.officialRole || 'Referee',
          
          // Home team
          homeTeamName: createMatchDto.homeTeamName,
          homeTeamShortName: createMatchDto.homeTeamShortName,
          homeKitBase: createMatchDto.homeKitBase || '#FF0000',
          homeKitStripe: createMatchDto.homeKitStripe || '#00FFFF',
          
          // Away team
          awayTeamName: createMatchDto.awayTeamName,
          awayTeamShortName: createMatchDto.awayTeamShortName,
          awayKitBase: createMatchDto.awayKitBase || '#FFFFFF',
          awayKitStripe: createMatchDto.awayKitStripe || '#0000FF',
          
          // Match format
          teamSize: createMatchDto.teamSize || 11,
          benchSize: createMatchDto.benchSize || 5,
          numberOfPeriods: createMatchDto.numberOfPeriods || 2,
          periodLengths: createMatchDto.periodLengths || [45, 45],
          halfTime: createMatchDto.halfTime || 15,
          
          // Extra time
          extraTime: createMatchDto.extraTime || false,
          extraTimeLengths: createMatchDto.extraTimeLengths,
          
          // Substitutions
          subOpportunities: createMatchDto.subOpportunities || false,
          subOpportunitiesAllowance: createMatchDto.subOpportunitiesAllowance || 1,
          extraTimeSubOpportunities: createMatchDto.extraTimeSubOpportunities || false,
          extraTimeSubOpportunitiesAllowance: createMatchDto.extraTimeSubOpportunitiesAllowance || 1,
          
          // Other rules
          penalties: createMatchDto.penalties || false,
          misconductCode: createMatchDto.misconductCode || 'England',
          temporaryDismissals: createMatchDto.temporaryDismissals || false,
          temporaryDismissalsTime: createMatchDto.temporaryDismissalsTime || 10,
          
          // Injury time
          injuryTimeAllowance: createMatchDto.injuryTimeAllowance || false,
          injuryTimeSubs: createMatchDto.injuryTimeSubs || 0,
          injuryTimeSanctions: createMatchDto.injuryTimeSanctions || 0,
          injuryTimeGoals: createMatchDto.injuryTimeGoals || 0,
          
          // Officials and other data
          matchOfficials: createMatchDto.matchOfficials || [],
          fees: createMatchDto.fees,
          expenses: createMatchDto.expenses,
          mileage: createMatchDto.mileage,
          travelTime: createMatchDto.travelTime,
          notes: createMatchDto.notes,
          templateName: createMatchDto.templateName,

          // Relations
          refereeId: actualRefereeId,
          status: MatchStatus.SCHEDULED
        },
        include: {
          referee: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          }
        }
      });

      return match;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to create match: ${error.message}`);
    }
  }

  async findAll(refereeId?: string): Promise<Match[]> {
    const where = refereeId ? { refereeId } : {};
    
    return this.prisma.match.findMany({
      where,
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        matchDate: 'asc'
      }
    });
  }

  async findUpcoming(refereeId?: string): Promise<Match[]> {
    const where = {
      matchDate: {
        gte: new Date()
      },
      status: {
        in: [MatchStatus.SCHEDULED]
      },
      ...(refereeId && { refereeId })
    };

    return this.prisma.match.findMany({
      where,
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        matchDate: 'asc'
      }
    });
  }

  async findPrevious(refereeId?: string): Promise<Match[]> {
    const where = {
      matchDate: {
        lt: new Date()
      },
      status: {
        in: [MatchStatus.COMPLETED, MatchStatus.CANCELLED]
      },
      ...(refereeId && { refereeId })
    };

    return this.prisma.match.findMany({
      where,
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        matchDate: 'desc'
      }
    });
  }

  async findOne(id: string): Promise<Match> {
    const match = await this.prisma.match.findUnique({
      where: { id },
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        assessments: true
      }
    });

    if (!match) {
      throw new NotFoundException(`Match with ID ${id} not found`);
    }

    return match;
  }

  async update(id: string, updateMatchDto: UpdateMatchDto): Promise<Match> {
    // Check if match exists
    await this.findOne(id);

    const updateData: any = {};
    
    // Only update fields that are provided
    Object.keys(updateMatchDto).forEach(key => {
      if (updateMatchDto[key] !== undefined) {
        if (key === 'matchDate') {
          updateData[key] = new Date(updateMatchDto[key]);
        } else {
          updateData[key] = updateMatchDto[key];
        }
      }
    });

    return this.prisma.match.update({
      where: { id },
      data: updateData,
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });
  }

  async remove(id: string): Promise<Match> {
    // Check if match exists
    await this.findOne(id);

    return this.prisma.match.delete({
      where: { id },
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });
  }

  async updateStatus(id: string, status: MatchStatus): Promise<Match> {
    // Check if match exists
    await this.findOne(id);

    return this.prisma.match.update({
      where: { id },
      data: { status },
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });
  }
}
