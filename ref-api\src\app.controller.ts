import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  getHealth(): { status: string; timestamp: string } {
    return {
      status: 'OK',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('test/database')
  async testDatabase(): Promise<{ service: string; result: string }> {
    const result = await this.appService.testDatabase();
    return { service: 'database', result };
  }

  @Get('test/redis')
  async testRedis(): Promise<{ service: string; result: string }> {
    const result = await this.appService.testRedis();
    return { service: 'redis', result };
  }

  @Get('test/matches')
  testMatches(): { message: string } {
    return { message: 'Matches module is loaded!' };
  }

  @Get('test/s3')
  testS3(): { service: string; result: string } {
    const result = this.appService.testS3();
    return { service: 's3', result };
  }
}
