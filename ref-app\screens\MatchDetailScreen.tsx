import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  TextInput,
  Dimensions,
  Platform,
  UIManager,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import Svg, { Path, Defs, ClipPath, G, Rect, Circle, Text as SvgText } from 'react-native-svg';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext'; // 1. Import useTheme

// --- Unchanged Setup ---
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const TABS = [ { key: 'overview', label: 'Overview' }, { key: 'team', label: 'Team' }, { key: 'match', label: 'Match' }, { key: 'stats', label: 'Stats' }, { key: 'earnings', label: 'Earnings' }, { key: 'evaluate', label: 'Evaluate' }, { key: 'notes', label: 'Notes' }, ];

// --- DYNAMIC ICONS ---
// These components are updated to accept a color prop from the theme.
const StripedTshirt: React.FC<{ baseColor: string; stripeColor: string; size?: number; }> = ({ baseColor, stripeColor, size = 28 }) => (
    <Svg width={size} height={size} viewBox="-1 0 19 19">
        <Defs><ClipPath id="matchDetailTshirtClip"><Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" /></ClipPath></Defs>
        <Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" fill={baseColor} />
        <G clipPath="url(#matchDetailTshirtClip)">{[3.5, 6, 8.5, 11, 13.5].map(x => (<Rect key={x} x={x} y="3" width="1.2" height="16" fill={stripeColor} />))}</G>
    </Svg>
);
const ShortsIcon = ({ color, size = 20 }: { color: string, size?: number }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path fill={color} d="M22,2H2A1,1,0,0,0,1,3V21a1,1,0,0,0,1,1H8a1,1,0,0,0,.868-.5L12,16.016l3.132,5.48A1,1,0,0,0,16,22h6a1,1,0,0,0,1-1V3A1,1,0,0,0,22,2Zm-1,9.9A5.013,5.013,0,0,1,17.1,8H21ZM21,6H13V4h8ZM3,4h8V6H3ZM3,8H6.9A5.013,5.013,0,0,1,3,11.9ZM16.58,20l-3.712-6.5a1.04,1.04,0,0,0-1.736,0L7.42,20H3V13.92A7,7,0,0,0,8.92,8H11V9a1,1,0,0,0,2,0V8h2.08A7,7,0,0,0,21,13.92V20Z" /></Svg> );
const RefereeIcon = ({ color }: { color: string }) => ( <Svg height="32" width="32" viewBox="2 2 24 24"><Path d="M12.375 21.375C10.5 21.375 8.90625 20.7188 7.59375 19.4062C6.28125 18.0938 5.625 16.5 5.625 14.625C5.625 14.4187 5.63437 14.2125 5.65312 14.0062C5.67188 13.8 5.7 13.5938 5.7375 13.3875C5.64375 13.425 5.53125 13.4531 5.4 13.4719C5.26875 13.4906 5.15625 13.5 5.0625 13.5C4.275 13.5 3.60938 13.2281 3.06562 12.6844C2.52187 12.1406 2.25 11.475 2.25 10.6875C2.25 9.9 2.50781 9.23438 3.02344 8.69062C3.53906 8.14687 4.19062 7.875 4.97812 7.875C5.59687 7.875 6.15469 8.04844 6.65156 8.39531C7.14844 8.74219 7.5 9.1875 7.70625 9.73125C8.325 9.16875 9.03281 8.71875 9.82969 8.38125C10.6266 8.04375 11.475 7.875 12.375 7.875H24.75V12.375H19.125V14.625C19.125 16.5 18.4688 18.0938 17.1562 19.4062C15.8438 20.7188 14.25 21.375 12.375 21.375ZM5.0625 11.8125C5.38125 11.8125 5.64844 11.7047 5.86406 11.4891C6.07969 11.2734 6.1875 11.0062 6.1875 10.6875C6.1875 10.3687 6.07969 10.1016 5.86406 9.88594C5.64844 9.67031 5.38125 9.5625 5.0625 9.5625C4.74375 9.5625 4.47656 9.67031 4.26094 9.88594C4.04531 10.1016 3.9375 10.3687 3.9375 10.6875C3.9375 11.0062 4.04531 11.2734 4.26094 11.4891C4.47656 11.7047 4.74375 11.8125 5.0625 11.8125ZM12.375 18.5625C13.4625 18.5625 14.3906 18.1781 15.1594 17.4094C15.9281 16.6406 16.3125 15.7125 16.3125 14.625C16.3125 13.5375 15.9281 12.6094 15.1594 11.8406C14.3906 11.0719 13.4625 10.6875 12.375 10.6875C11.2875 10.6875 10.3594 11.0719 9.59062 11.8406C8.82187 12.6094 8.4375 13.5375 8.4375 14.625C8.4375 15.7125 8.82187 16.6406 9.59062 17.4094C10.3594 18.1781 11.2875 18.5625 12.375 18.5625Z" fill={color}/></Svg>);
const AssistantIcon = ({ color }: { color: string }) => ( <Svg height="28" width="28" viewBox="0 0 24 24"><Path d="M9 6H11V4H9V6ZM13 6V4H15V6H13ZM9 14V12H11V14H9ZM17 10V8H19V10H17ZM17 14V12H19V14H17ZM13 14V12H15V14H13ZM17 6V4H19V6H17ZM11 8V6H13V8H11ZM5 20V4H7V6H9V8H7V10H9V12H7V20H5ZM15 12V10H17V12H15ZM11 12V10H13V12H11ZM9 10V8H11V10H9ZM13 10V8H15V10H13ZM15 8V6H17V8H15Z" fill={color}/></Svg>);
const FourthOfficialIcon = ({ color }: { color: string }) => (
    <Svg width="24" height="24" viewBox="0 0 24 24">
      <Rect x="2" y="2" width="20" height="20" rx="4" stroke={color} strokeWidth="1.5" fill="none" />
      <SvgText x="12" y="16" textAnchor="middle" fontSize="12" fontWeight="600" fill={color}>4th</SvgText>
    </Svg>
);
const VarIcon = ({ color, size = 24 }: { color: string; size?: number }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /><Path d="M12 15a3 3 0 100-6 3 3 0 000 6z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /></Svg> );
const UserIcon = ({ color, size = 20 }: { color: string; size?: number }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /><Circle cx="12" cy="7" r="4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></Svg> );

// --- Content Components ---
// These now receive `styles` as a prop to handle dynamic theming.
const OverviewContent = ({ styles }: { styles: any }) => {
  const { theme } = useTheme();
  const [psgOfficials, setPsgOfficials] = useState([{ id: 1, teamName: 'PSG', managerName: 'Manager Name' }]);
  const [fcbOfficials, setFcbOfficials] = useState([{ id: 1, teamName: 'FCB', managerName: 'Manager Name' }]);
  // ... local state ...
  const CountdownItem = ({ value, label }: { value: string; label: string }) => (<View style={styles.countdownItem}><Text style={styles.countdownValue}>{value}</Text><Text style={styles.countdownLabel}>{label}</Text></View>);
  const DetailRow = ({ label, value }: { label: string; value: string }) => (<View style={styles.detailRow}><Text style={styles.detailLabel}>{label}</Text><View style={styles.valueContainer}><Text style={styles.detailValue}>{value}</Text></View></View>);
  const OfficialRow = ({ icon, name, onSync, label, }: { icon: React.ReactNode; name: string; onSync?: () => void; label?: string; }) => ( <View style={styles.officialRow}><View style={styles.officialIconContainer}>{icon}{label && <Text style={styles.officialIconLabel}>{label}</Text>}</View><Text style={styles.officialName}>{name}</Text>{onSync && (<TouchableOpacity style={styles.syncButton} onPress={onSync}><Text style={styles.syncButtonText}>Sync now</Text></TouchableOpacity>)}</View> );
  const TeamOfficialRow = ({ kit, managerName, onSync, }: { kit: React.ReactNode; managerName: string; onSync?: () => void; }) => (<View style={styles.teamOfficialRow}><View style={styles.teamOfficialKitContainer}>{kit}</View><View style={styles.teamOfficialInfoContainer}><Text style={styles.teamOfficialManagerName}>{managerName}</Text></View>{onSync && (<TouchableOpacity style={styles.syncButton} onPress={onSync}><Text style={styles.syncButtonText}>Sync now</Text></TouchableOpacity>)}</View>);
  const addPsgOfficial = () => setPsgOfficials([...psgOfficials, { id: psgOfficials.length + 1, teamName: 'PSG', managerName: 'Manager Name' },]);
  const addFcbOfficial = () => setFcbOfficials([...fcbOfficials, { id: fcbOfficials.length + 1, teamName: 'FCB', managerName: 'Manager Name' },]);

  return (
    <View>
      <Text style={styles.countdownTitle}>The Match Kicks off in</Text>
      <View style={styles.countdownContainer}><CountdownItem value="5" label="Days" /><View style={styles.countdownSeparator} /><CountdownItem value="21" label="Hours" /><View style={styles.countdownSeparator} /><CountdownItem value="30" label="Mins" /></View>
      <View style={styles.card}><Text style={styles.cardTitle}>Match details</Text><DetailRow label="Kick-off" value="Sat 03-05-2025   15:30" /><DetailRow label="Competition" value="UEFA Champions League" /><DetailRow label="Venue" value="Saxophone stadium" /><DetailRow label="Tag" value="Etc" /></View>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Match Officials</Text>
        <OfficialRow icon={<RefereeIcon color={theme.primary}/>} name="Dave JOHN" />
        <OfficialRow icon={<AssistantIcon color={theme.primary}/>} name="Dave Mark" onSync={() => {}} label="Ar1" />
        <OfficialRow icon={<AssistantIcon color={theme.primary}/>} name="Lave Maring" onSync={() => {}} label="Ar2" />
        <OfficialRow icon={<FourthOfficialIcon color={theme.text}/>} name="Liam Dave" onSync={() => {}} />
        <OfficialRow icon={<VarIcon color={theme.primary} />} name="Liam Dave" onSync={() => {}} />
      </View>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Team Officials</Text>
        <View style={styles.teamSectionWrapper}><View style={styles.teamSectionHeader}><View style={styles.teamTag}><Text style={styles.teamTagText}>PSG Team</Text></View><TouchableOpacity onPress={addPsgOfficial} style={styles.addButton}><UserIcon color={theme.primary} size={20} /></TouchableOpacity></View>{psgOfficials.map((official) => (<TeamOfficialRow key={official.id} kit={<><StripedTshirt baseColor="#DA291C" stripeColor="#000" size={28} /><ShortsIcon color="#000" size={16} /></>} managerName={official.managerName} onSync={() => {}} />))}</View>
        <View style={styles.teamSectionWrapper}><View style={styles.teamSectionHeader}><View style={styles.teamTag}><Text style={styles.teamTagText}>FCB Team</Text></View><TouchableOpacity onPress={addFcbOfficial} style={styles.addButton}><UserIcon color={theme.primary} size={20} /></TouchableOpacity></View>{fcbOfficials.map((official) => (<TeamOfficialRow key={official.id} kit={<><StripedTshirt baseColor="#004193" stripeColor="#E30613" size={28} /><ShortsIcon color="#000" size={16} /></>} managerName={official.managerName} onSync={() => {}} />))}</View>
      </View>
    </View>
  );
};
const EarningsContent = ({ styles }: { styles: any }) => {
    const { theme } = useTheme();
    const [fees, setFees] = useState('2');
    const [expenses, setExpenses] = useState('2');
    const [travelTime, setTravelTime] = useState('2h 33min');
    const [travelMileage, setTravelMileage] = useState('95 KM');
    const EarningInputRow = ({ label, value, setValue, placeholder, keyboardType = 'default' }: { label: string; value: string; setValue: (text: string) => void; placeholder: string; keyboardType?: any; }) => (<View style={styles.inputRow}><Text style={styles.inputLabel}>{label}</Text><TextInput style={styles.inputValue} value={value} onChangeText={setValue} placeholder={placeholder} placeholderTextColor={theme.textSecondary} keyboardType={keyboardType} /></View>);
    return (
        <View style={styles.earningsContainer}>
            <EarningInputRow label="Fees" value={fees} setValue={setFees} placeholder="$0" keyboardType="numeric" />
            <EarningInputRow label="Expenses" value={expenses} setValue={setExpenses} placeholder="$0" keyboardType="numeric" />
            <EarningInputRow label="Travel Time" value={travelTime} setValue={setTravelTime} placeholder="0h 0m" />
            <EarningInputRow label="Travel Mileage" value={travelMileage} setValue={setTravelMileage} placeholder="0 KM" keyboardType="numeric" />
            <TouchableOpacity style={styles.inputRow}>
                <Text style={styles.inputLabel}>Attach Receipt <Text style={{fontWeight: 'normal'}}>(optional)</Text></Text>
                <MaterialIcons name="attachment" size={22} color={theme.textSecondary} />
            </TouchableOpacity>
            <TouchableOpacity style={[styles.primaryButton, { marginTop: 8 }]}>
                <Text style={styles.primaryButtonText}>Save Earnings</Text>
            </TouchableOpacity>
        </View>
    );
};
const NotesContent = ({ styles }: { styles: any }) => {
    const { theme } = useTheme();
    const [note, setNote] = useState('');
    return (
        <View style={styles.notesContainer}>
            <View style={styles.notesCard}><TextInput style={styles.notesTextInput} multiline placeholder="Add your notes here..." placeholderTextColor={theme.textSecondary} value={note} onChangeText={setNote} /></View>
            <TouchableOpacity style={[styles.primaryButton, { marginTop: 16 }]}><Text style={styles.primaryButtonText}>Save Notes</Text></TouchableOpacity>
        </View>
    );
};


// --- MAIN COMPONENT ---
const MatchDetailScreen = () => {
  const navigation = useNavigation();
  const { theme, isDarkMode } = useTheme(); // 2. Use the theme hook
  const styles = getStyles(theme, isDarkMode); // 3. Get dynamic styles
  
  const [activeTab, setActiveTab] = useState('overview');
  const [activeTeamView, setActiveTeamView] = useState('Home');
  const scrollViewRef = useRef<ScrollView>(null);
  const [tabLayouts, setTabLayouts] = useState<Record<number, {x: number, width: number}>>({});
  const activeTabIndex = TABS.findIndex(tab => tab.key === activeTab);
  useEffect(() => { if (tabLayouts[activeTabIndex]) { const { x, width } = tabLayouts[activeTabIndex]; const scrollToX = x - SCREEN_WIDTH / 2 + width / 2; scrollViewRef.current?.scrollTo({ x: scrollToX, animated: true }); } }, [activeTabIndex, tabLayouts]);
  
  const TeamDisplay = ({ kit, name }: { kit: { base: string; stripe: string, shorts: string }, name: string }) => ( <View style={styles.teamDisplayContainer}><StripedTshirt baseColor={kit.base} stripeColor={kit.stripe} size={48} /><ShortsIcon color={kit.shorts} size={26} /><Text style={[styles.teamName, { color: kit.base }]}>{name}</Text></View> );
  const IconBoxButton = ({ iconName }: { iconName: React.ComponentProps<typeof MaterialIcons>['name'] }) => ( <TouchableOpacity style={styles.iconButton}><MaterialIcons name={iconName} size={28} color={theme.primary} /></TouchableOpacity> );

  const renderContent = () => {
    // Pass the dynamic styles to the content components
    if (activeTab === 'earnings') { return <EarningsContent styles={styles} />; }
    if (activeTab === 'notes') { return <NotesContent styles={styles} />; }
    if (activeTab === 'overview') { return <OverviewContent styles={styles} />; }
    if (activeTab === 'team') {
      return (<><View style={styles.homeAwayToggle}><TouchableOpacity style={[styles.toggleButton, activeTeamView === 'Home' && styles.activeToggleButton]} onPress={() => setActiveTeamView('Home')}><Text style={[styles.toggleButtonText, activeTeamView === 'Home' && styles.activeToggleButtonText]}>HOME</Text></TouchableOpacity><TouchableOpacity style={[styles.toggleButton, activeTeamView === 'Away' && styles.activeToggleButton]} onPress={() => setActiveTeamView('Away')}><Text style={[styles.toggleButtonText, activeTeamView === 'Away' && styles.activeToggleButtonText]}>AWAY</Text></TouchableOpacity></View>{activeTeamView === 'Home' && (<><View style={styles.card}><Text style={styles.cardTitle}>Add Squad Sheet</Text><View style={styles.iconButtonsRow}><IconBoxButton iconName="file-upload" /><IconBoxButton iconName="image" /><IconBoxButton iconName="photo-camera" /></View><TouchableOpacity style={[styles.primaryButton, styles.edgeInset]}><Text style={styles.primaryButtonText}>Manually</Text></TouchableOpacity></View><View style={styles.card}><Text style={styles.cardTitle}>Add Squad Sheet By Email Request</Text><TextInput style={styles.emailInput} placeholder="Email..." placeholderTextColor={theme.textSecondary} keyboardType="email-address" autoCapitalize="none" /><TouchableOpacity style={[styles.primaryButton, styles.edgeInset]}><Text style={styles.primaryButtonText}>Request Squad Sheet</Text></TouchableOpacity></View></>)}{activeTeamView === 'Away' && (<View style={styles.card}><Text style={styles.cardTitle}>Away Team Content</Text></View>)}</>);
    }
    return <View style={styles.card}><Text style={styles.cardTitle}>{TABS.find(t=>t.key === activeTab)?.label} Content</Text></View>;
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <Header title="Match Details" showBackButton={true} rightComponent={<TouchableOpacity onPress={() => {}}><MaterialIcons name="add" size={24} color={theme.primary} /></TouchableOpacity>} />
      <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent} nestedScrollEnabled={true}>
        <View style={styles.card}><Text style={styles.leagueTitle}>UEFA Champions league</Text><View style={styles.separator} /><View style={styles.teamsRow}><TeamDisplay kit={{ base: '#004193', stripe: '#E30613', shorts: '#000000' }} name="PSG" /><Text style={styles.vsText}>-</Text><TeamDisplay kit={{ base: '#DA291C', stripe: '#000000', shorts: '#000000' }} name="MAN" /></View></View>
        
        <View style={styles.tabsContainer}><ScrollView ref={scrollViewRef} horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabsScrollContainer} bounces={false}>{TABS.map((tab, index) => ( <TouchableOpacity key={tab.key} onLayout={(event) => { const { x, width } = event.nativeEvent.layout; setTabLayouts((prev) => ({ ...prev, [index]: { x, width } })); }} style={[styles.tab, activeTab === tab.key && styles.activeTab]} onPress={() => setActiveTab(tab.key)} activeOpacity={0.8}><Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>{tab.label}</Text></TouchableOpacity> ))}</ScrollView></View>
        {renderContent()}
      </ScrollView>
    </SafeAreaView>
  );
};


// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  // General
  safeArea: { flex: 1, backgroundColor: theme.card },
  container: { flex: 1, backgroundColor: theme.background },
  scrollContent: { padding: 16, paddingBottom: 80 },
  card: { 
    backgroundColor: theme.card, 
    borderRadius: 16, 
    paddingVertical: 12, 
    marginBottom: 16, 
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.05,
    shadowRadius: 4,
    elevation: isDarkMode ? 0 : 2,
  },
  cardTitle: { fontSize: 18, fontWeight: 'bold', color: theme.text, marginBottom: 16, paddingHorizontal: 16, paddingTop: 4 },
  primaryButton: { backgroundColor: theme.primary, paddingVertical: 12, borderRadius: 10, alignItems: 'center', },
  edgeInset: {marginHorizontal: 16,},
  primaryButtonText: { color: theme.white, fontSize: 15, fontWeight: 'bold', },
  separator: { height: 1, backgroundColor: theme.border, },
  
  // Header section
  leagueTitle: { fontSize: 16, fontWeight: '600', color: theme.text, textAlign: 'center', paddingBottom: 12, },
  teamsRow: { flexDirection: 'row', justifyContent: 'space-around', alignItems: 'center', paddingTop: 16, paddingHorizontal: 16, },
  teamDisplayContainer: { alignItems: 'center', width: 100, },
  teamName: { marginTop: 4, fontSize: 16, fontWeight: 'bold', },
  vsText: { fontSize: 20, fontWeight: '300', color: theme.textSecondary, marginTop: -40, },

  // Tabs
  tabsContainer: { backgroundColor: theme.background, paddingVertical: 8, paddingHorizontal: 0, marginBottom: 8, },
  tabsScrollContainer: { paddingHorizontal: 16, },
  tab: { 
    paddingHorizontal: 16, 
    paddingVertical: 8, 
    backgroundColor: theme.card, 
    borderRadius: 20, 
    marginRight: 8, 
    borderWidth: 1, 
    borderColor: theme.border, 
    alignItems: 'center', 
    justifyContent: 'center',
  },
  activeTab: { 
    backgroundColor: theme.primary, 
    borderColor: theme.primary, 
  },
  tabText: { fontSize: 14, fontWeight: '600', textAlign: 'center', color: theme.text, },
  activeTabText: { color:theme.background, },

  // Team Tab
  homeAwayToggle: { 
    flexDirection: 'row', 
    backgroundColor: `${theme.text}12`, 
    borderRadius: 10, 
    padding: 4, 
    marginBottom: 16,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  toggleButton: { flex: 1, paddingVertical: 10, borderRadius: 8, alignItems: 'center', },
  activeToggleButton: { backgroundColor: theme.primary, },
  toggleButtonText: { color: theme.text, fontWeight: 'bold', fontSize: 13, },
  activeToggleButtonText: { color: theme.background },
  iconButtonsRow: { flexDirection: 'row', justifyContent: 'space-around', marginBottom: 20, paddingHorizontal: 20, },
  iconButton: { width: 52, height: 52, borderRadius: 12, backgroundColor: `${theme.primary}26`, justifyContent: 'center', alignItems: 'center', },
  emailInput: { backgroundColor: theme.background, borderWidth: 1, borderColor: theme.border, borderRadius: 8, padding: 12, fontSize: 14, marginBottom: 12, marginHorizontal: 16, color: theme.text },
  
  // Overview Content
  countdownTitle: { textAlign: 'center', color: theme.textSecondary, marginBottom: 8, fontSize: 13, fontWeight: '500' },
  countdownContainer: { 
    flexDirection: 'row', 
    justifyContent: 'center', 
    alignItems: 'center', 
    backgroundColor: theme.card, 
    borderRadius: 12, 
    borderWidth: 1, 
    borderColor: theme.border, 
    paddingVertical: 12, 
    marginBottom: 16, 
    alignSelf: 'center', 
    paddingHorizontal: 20 
  },
  countdownItem: { alignItems: 'center', minWidth: 55, },
  countdownValue: { fontSize: 24, fontWeight: 'bold', color: theme.text },
  countdownLabel: { fontSize: 11, color: theme.textSecondary, marginTop: 2 },
  countdownSeparator: { width: 1, height: 30, backgroundColor: theme.border, marginHorizontal: 15, },
  detailRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: theme.background, borderRadius: 10, padding: 14, marginBottom: 10, marginHorizontal: 16, },
  detailLabel: { fontSize: 14, color: theme.text, fontWeight: '600', marginRight: 10, },
  valueContainer: { flex: 1, alignItems: 'flex-end', },
  detailValue: { fontSize: 14, color: theme.textSecondary, fontWeight: '500', textAlign: 'right', },
  officialRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: theme.background, borderRadius: 10, paddingVertical: 10, paddingHorizontal: 14, marginBottom: 10, marginHorizontal: 16, },
  officialIconContainer: { alignItems: 'center', marginRight: 12, width: 32 },
  officialIconLabel: { fontSize: 10, fontWeight: 'bold', color: theme.textSecondary, marginTop: 2 },
  officialName: { flex: 1, fontSize: 14, color: theme.text, fontWeight: '600' },
  syncButton: { borderWidth: 1, borderColor: theme.border, borderRadius: 15, paddingHorizontal: 12, paddingVertical: 5, backgroundColor: theme.card, minWidth: 75, alignItems: 'center' },
  syncButtonText: { fontSize: 11, color: theme.textSecondary, fontWeight: '500' },
  teamSectionWrapper: { paddingHorizontal: 16, marginBottom: 16 },
  teamSectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 },
  teamTag: { backgroundColor: theme.primary, borderRadius: 15, paddingHorizontal: 10, paddingVertical: 5 },
  teamTagText: { color: theme.white, fontSize: 11, fontWeight: 'bold' },
  addButton: { padding: 6, borderRadius: 20, backgroundColor: `${theme.primary}1A`, },
  teamOfficialRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: theme.background, borderRadius: 10, paddingVertical: 12, paddingHorizontal: 12, marginBottom: 8, minHeight: 60, },
  teamOfficialKitContainer: { flexDirection: 'column', alignItems: 'center', justifyContent: 'center', marginRight: 12, width: 32 },
  teamOfficialInfoContainer: { flex: 1, marginRight: 12, minWidth: 0 },
  teamOfficialManagerName: { fontSize: 13, color: theme.text, fontWeight: '500', flexWrap: 'wrap', lineHeight: 18 },

  // Earnings Content
  earningsContainer: {},
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.card,
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 6,
    borderWidth: 1,
    borderColor: theme.border
  },
  inputLabel: {
    fontSize: 14,
    color: theme.text,
    fontWeight: 'bold'
  },
  inputValue: {
    fontSize: 14,
    color: theme.text,
    fontWeight: 'bold',
    textAlign: 'right'
  },
  
  // Notes Content
  notesContainer: {},
  notesCard: { backgroundColor: theme.card, borderRadius: 12, borderWidth: 1, borderColor: theme.border, minHeight: 200 },
  notesTextInput: { padding: 14, fontSize: 15, color: theme.text, textAlignVertical: 'top', flex: 1 },
});


export default MatchDetailScreen;