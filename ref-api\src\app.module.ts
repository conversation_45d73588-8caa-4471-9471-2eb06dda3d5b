import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaService } from './prisma.service';
import { RedisModule } from './redis/redis.module';
import { S3Module } from './s3/s3.module';
import { AuthModule } from './auth/auth.module';
import { MatchesModule } from './matches/matches.module';

@Module({
  imports: [RedisModule, S3Module, AuthModule, MatchesModule],
  controllers: [AppController],
  providers: [AppService, PrismaService],
})
export class AppModule {}
