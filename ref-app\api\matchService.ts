import { apiClient } from './client';
import { API_CONFIG, ApiResponse, CreateMatchRequest, MatchResponse } from './config';

export class MatchService {
  // Create a new match
  static async createMatch(matchData: CreateMatchRequest): Promise<ApiResponse<MatchResponse>> {
    return apiClient.post<MatchResponse>(API_CONFIG.ENDPOINTS.MATCHES.BASE, matchData);
  }

  // Get all matches
  static async getAllMatches(refereeId?: string): Promise<ApiResponse<MatchResponse[]>> {
    const params = refereeId ? { refereeId } : undefined;
    return apiClient.get<MatchResponse[]>(API_CONFIG.ENDPOINTS.MATCHES.BASE, params);
  }

  // Get upcoming matches
  static async getUpcomingMatches(refereeId?: string): Promise<ApiResponse<MatchResponse[]>> {
    const params = refereeId ? { refereeId } : undefined;
    return apiClient.get<MatchResponse[]>(API_CONFIG.ENDPOINTS.MATCHES.UPCOMING, params);
  }

  // Get previous matches
  static async getPreviousMatches(refereeId?: string): Promise<ApiResponse<MatchResponse[]>> {
    const params = refereeId ? { refereeId } : undefined;
    return apiClient.get<MatchResponse[]>(API_CONFIG.ENDPOINTS.MATCHES.PREVIOUS, params);
  }

  // Get match by ID
  static async getMatchById(id: string): Promise<ApiResponse<MatchResponse>> {
    return apiClient.get<MatchResponse>(API_CONFIG.ENDPOINTS.MATCHES.BY_ID(id));
  }

  // Update match
  static async updateMatch(id: string, matchData: Partial<CreateMatchRequest>): Promise<ApiResponse<MatchResponse>> {
    return apiClient.patch<MatchResponse>(API_CONFIG.ENDPOINTS.MATCHES.BY_ID(id), matchData);
  }

  // Update match status
  static async updateMatchStatus(
    id: string, 
    status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  ): Promise<ApiResponse<MatchResponse>> {
    return apiClient.patch<MatchResponse>(
      API_CONFIG.ENDPOINTS.MATCHES.UPDATE_STATUS(id), 
      { status }
    );
  }

  // Delete match
  static async deleteMatch(id: string): Promise<ApiResponse<MatchResponse>> {
    return apiClient.delete<MatchResponse>(API_CONFIG.ENDPOINTS.MATCHES.BY_ID(id));
  }

  // Helper method to convert form data to API format
  static convertFormDataToApiFormat(formData: any): CreateMatchRequest {
    return {
      // Basic match info
      competition: formData.competition,
      venue: formData.venue,
      matchDate: formData.matchDate || new Date().toISOString(),
      officialRole: formData.officialRole || 'Referee',

      // Home team
      homeTeamName: formData.homeTeamName,
      homeTeamShortName: formData.homeTeamShortName,
      homeKitBase: formData.homeKitColors?.base || '#FF0000',
      homeKitStripe: formData.homeKitColors?.stripe || '#00FFFF',

      // Away team
      awayTeamName: formData.awayTeamName,
      awayTeamShortName: formData.awayTeamShortName,
      awayKitBase: formData.awayKitColors?.base || '#FFFFFF',
      awayKitStripe: formData.awayKitColors?.stripe || '#0000FF',

      // Match format
      teamSize: formData.teamSize || 11,
      benchSize: formData.benchSize || 5,
      numberOfPeriods: formData.numberOfPeriods || 2,
      periodLengths: formData.periodLengths || [45, 45],
      halfTime: formData.halfTime || 15,

      // Extra time settings
      extraTime: formData.extraTime === 'Yes',
      extraTimeLengths: formData.extraTime === 'Yes' ? formData.extraTimeLengths : undefined,

      // Substitution rules
      subOpportunities: formData.SubOpportunities === 'Yes',
      subOpportunitiesAllowance: formData.SubOpportunitiesAllowance || 1,
      extraTimeSubOpportunities: formData.extraTimeSubOpportunities === 'Yes',
      extraTimeSubOpportunitiesAllowance: formData.extraTimeSubOpportunitiesAllowance || 1,

      // Other rules
      penalties: formData.penalties === 'Yes',
      misconductCode: formData.misconductCode || 'England',
      temporaryDismissals: formData.temporaryDismissals === 'Yes',
      temporaryDismissalsTime: formData.temporaryDismissalsTime || 10,

      // Injury time
      injuryTimeAllowance: formData.injuryTimeAllowance === 'Yes',
      injuryTimeSubs: formData.injuryTime?.subs || 0,
      injuryTimeSanctions: formData.injuryTime?.sanctions || 0,
      injuryTimeGoals: formData.injuryTime?.goals || 0,

      // Match officials
      matchOfficials: formData.matchOfficialsList?.map((official: any) => ({
        role: official.role,
        name: official.name
      })) || [],

      // Earnings
      fees: formData.fees,
      expenses: formData.expenses,
      mileage: formData.mileage,
      travelTime: formData.travelTime,

      // Notes
      notes: formData.noteContent,

      // Template info
      templateName: formData.selectedTemplate !== '__CREATE_NEW__' ? formData.selectedTemplate : formData.newTemplateName,
    };
  }

  // Helper method to convert API response to dashboard format
  static convertApiResponseToDashboardFormat(matches: MatchResponse[]): any[] {
    return matches.map(match => ({
      division: match.competition,
      role: match.officialRole,
      homeTeam: {
        name: match.homeTeamName,
        kitColor: match.homeKitBase
      },
      awayTeam: {
        name: match.awayTeamName,
        kitColor: match.awayKitBase
      },
      location: match.venue,
      date: new Date(match.matchDate).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      }),
      time: new Date(match.matchDate).toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit'
      }),
      id: match.id,
      status: match.status
    }));
  }
}

export default MatchService;
