import { IsString, IsOptional, IsBoolean, IsInt, IsArray, IsDateString, Min, IsJSO<PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateMatchDto {
  // Basic match info
  @IsString()
  competition: string;

  @IsString()
  venue: string;

  @IsDateString()
  matchDate: string;

  @IsOptional()
  @IsString()
  officialRole?: string = 'Referee';

  // Home team
  @IsString()
  homeTeamName: string;

  @IsOptional()
  @IsString()
  homeTeamShortName?: string;

  @IsOptional()
  @IsString()
  homeKitBase?: string = '#FF0000';

  @IsOptional()
  @IsString()
  homeKitStripe?: string = '#00FFFF';

  // Away team
  @IsString()
  awayTeamName: string;

  @IsOptional()
  @IsString()
  awayTeamShortName?: string;

  @IsOptional()
  @IsString()
  awayKitBase?: string = '#FFFFFF';

  @IsOptional()
  @IsString()
  awayKitStripe?: string = '#0000FF';

  // Match format
  @IsOptional()
  @IsInt()
  @Min(1)
  teamSize?: number = 11;

  @IsOptional()
  @IsInt()
  @Min(0)
  benchSize?: number = 5;

  @IsOptional()
  @IsInt()
  @Min(1)
  numberOfPeriods?: number = 2;

  @IsOptional()
  @IsArray()
  periodLengths?: number[] = [45, 45];

  @IsOptional()
  @IsInt()
  @Min(0)
  halfTime?: number = 15;

  // Extra time settings
  @IsOptional()
  @IsBoolean()
  extraTime?: boolean = false;

  @IsOptional()
  @IsArray()
  extraTimeLengths?: [number, number];

  // Substitution rules
  @IsOptional()
  @IsBoolean()
  subOpportunities?: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(0)
  subOpportunitiesAllowance?: number = 1;

  @IsOptional()
  @IsBoolean()
  extraTimeSubOpportunities?: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(0)
  extraTimeSubOpportunitiesAllowance?: number = 1;

  // Other rules
  @IsOptional()
  @IsBoolean()
  penalties?: boolean = false;

  @IsOptional()
  @IsString()
  misconductCode?: string = 'England';

  @IsOptional()
  @IsBoolean()
  temporaryDismissals?: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(0)
  temporaryDismissalsTime?: number = 10;

  // Injury time
  @IsOptional()
  @IsBoolean()
  injuryTimeAllowance?: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(0)
  injuryTimeSubs?: number = 0;

  @IsOptional()
  @IsInt()
  @Min(0)
  injuryTimeSanctions?: number = 0;

  @IsOptional()
  @IsInt()
  @Min(0)
  injuryTimeGoals?: number = 0;

  // Match officials
  @IsOptional()
  @IsArray()
  matchOfficials?: Array<{ role: string; name: string }>;

  // Earnings
  @IsOptional()
  @IsString()
  fees?: string;

  @IsOptional()
  @IsString()
  expenses?: string;

  @IsOptional()
  @IsString()
  mileage?: string;

  @IsOptional()
  @IsString()
  travelTime?: string;

  // Notes
  @IsOptional()
  @IsString()
  notes?: string;

  // Template info
  @IsOptional()
  @IsString()
  templateName?: string;
}
