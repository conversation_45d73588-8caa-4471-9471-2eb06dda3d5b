// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  output        = "../node_modules/.prisma/client"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for referees, assessors, admins, etc.
model User {
  id            String   @id @default(cuid())
  email         String   @unique
  name          String
  displayName   String?
  photoURL      String?
  firebaseUid   String?  @unique
  emailVerified Boolean  @default(false)
  provider      String   @default("email")
  lastLoginAt   DateTime?
  role          UserRole @default(REFEREE)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  matches     Match[]
  assessments Assessment[]

  @@map("users")
}

// Match model for tracking games
model Match {
  id          String      @id @default(cuid())

  // Basic match info
  competition String
  venue       String
  matchDate   DateTime
  officialRole String     @default("Referee")

  // Home team
  homeTeamName      String
  homeTeamShortName String?
  homeKitBase       String  @default("#FF0000")
  homeKitStripe     String  @default("#00FFFF")

  // Away team
  awayTeamName      String
  awayTeamShortName String?
  awayKitBase       String  @default("#FFFFFF")
  awayKitStripe     String  @default("#0000FF")

  // Match format
  teamSize              Int     @default(11)
  benchSize             Int     @default(5)
  numberOfPeriods       Int     @default(2)
  periodLengths         Json    // Array of period lengths
  halfTime              Int     @default(15)

  // Extra time settings
  extraTime             Boolean @default(false)
  extraTimeLengths      Json?   // [period1, period2] lengths

  // Substitution rules
  subOpportunities                    Boolean @default(false)
  subOpportunitiesAllowance          Int     @default(1)
  extraTimeSubOpportunities          Boolean @default(false)
  extraTimeSubOpportunitiesAllowance Int     @default(1)

  // Other rules
  penalties             Boolean @default(false)
  misconductCode        String  @default("England")
  temporaryDismissals   Boolean @default(false)
  temporaryDismissalsTime Int   @default(10)

  // Injury time
  injuryTimeAllowance   Boolean @default(false)
  injuryTimeSubs        Int     @default(0)
  injuryTimeSanctions   Int     @default(0)
  injuryTimeGoals       Int     @default(0)

  // Match officials (JSON array)
  matchOfficials        Json    // Array of {role, name}

  // Earnings
  fees        String?
  expenses    String?
  mileage     String?
  travelTime  String?

  // Notes
  notes       String?

  // Template info
  templateName String?

  // Status and metadata
  status      MatchStatus @default(SCHEDULED)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  referee     User   @relation(fields: [refereeId], references: [id])
  refereeId   String
  assessments Assessment[]

  @@map("matches")
}

// Assessment model for referee evaluations
model Assessment {
  id        String   @id @default(cuid())
  score     Int
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  match     Match  @relation(fields: [matchId], references: [id])
  matchId   String
  assessor  User   @relation(fields: [assessorId], references: [id])
  assessorId String

  @@map("assessments")
}

// Enums
enum UserRole {
  REFEREE
  AR1
  AR2
  FOURTH_OFFICIAL
  ASSESSOR
  ADMIN
  MANAGER
}

enum MatchStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}
