import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Header from '../components/Header';
import Svg, { Defs, ClipPath, Path, G, Rect } from 'react-native-svg';
import { useTheme } from '../contexts/ThemeContext';
import MatchService from '../api/matchService';

const { width } = Dimensions.get('window');

// --- INTERFACES AND TYPES ---
type Team = {
  name: string;
  kitColor: string;
};

type Match = {
  division: string;
  role: string;
  homeTeam: Team;
  awayTeam: Team;
  location: string;
  date: string;
  time: string;
  homeScore?: number;
  awayScore?: number;
};

interface StripedTshirtProps {
  baseColor: string;
  size?: number;
}

// --- DATA ---
// Static data as fallback - will be replaced with API data
const fallbackUpcomingMatches: Match[] = [
  { division: "East Gwent Division", role: "Referee", homeTeam: { name: "Town MAN Reserves", kitColor: "#0066cc" }, awayTeam: { name: "Chepstow Town PSG 3rd Team Dot dave playeing", kitColor: "#cc0000" }, location: "Manhattan City", date: "05.25.25", time: "19:35" },
  { division: "East Gwent Division", role: "Ar2", homeTeam: { name: "Monmouth Town FC Reserves", kitColor: "#9933cc" }, awayTeam: { name: "Manchester United", kitColor: "#66cc00" }, location: "New York City", date: "05.25.25", time: "19:35" },
];

const fallbackPreviousMatches: Match[] = [
  { division: "West Gwent Division", role: "Referee", homeTeam: { name: "Caldicot Town", kitColor: "#ff9900" }, awayTeam: { name: "Chepstow Town PSG 3rd Team", kitColor: "#003366" }, location: "Jubilee Way", date: "04.15.25", time: "14:00", homeScore: 2, awayScore: 4 },
  { division: "Gwent County League", role: "Ar1", homeTeam: { name: "Abercarn United", kitColor: "#000000" }, awayTeam: { name: "Blaenavon Blues", kitColor: "#3399ff" }, location: "The Cwmcarn", date: "04.08.25", time: "18:30", homeScore: 3, awayScore: 1 },
];

// --- SVG T-SHIRT COMPONENT ---
const StripedTshirt: React.FC<StripedTshirtProps> = ({ baseColor, size = 36 }) => (
  <Svg width={size} height={size} viewBox="-1 0 19 19">
    <Defs><ClipPath id="tshirtClip"><Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" /></ClipPath></Defs>
    <Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" fill={baseColor} />
    <G clipPath="url(#tshirtClip)">
        <Rect x="3.5" y="3" width="1.2" height="12" fill="#d60a2e" />
        <Rect x="6" y="3" width="1.2" height="12" fill="#d60a2e" />
        <Rect x="8.5" y="3" width="1.2" height="12" fill="#d60a2e" />
        <Rect x="11" y="3" width="1.2" height="12" fill="#d60a2e" />
        <Rect x="13.5" y="3" width="1.2" height="12" fill="#d60a2e" />
    </G>
  </Svg>
);

// --- MAIN DASHBOARD COMPONENT ---
const UserDashboard = () => {
  const navigation = useNavigation();
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);
  const [activeTab, setActiveTab] = useState('upcoming');

  // State for API data
  const [upcomingMatches, setUpcomingMatches] = useState<Match[]>([]);
  const [previousMatches, setPreviousMatches] = useState<Match[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch matches data
  const fetchMatches = async (showLoading = true) => {
    try {
      if (showLoading) setIsLoading(true);
      setError(null);

      // Fetch upcoming and previous matches
      const [upcomingResponse, previousResponse] = await Promise.all([
        MatchService.getUpcomingMatches(),
        MatchService.getPreviousMatches()
      ]);

      if (upcomingResponse.success && upcomingResponse.data) {
        const formattedUpcoming = MatchService.convertApiResponseToDashboardFormat(upcomingResponse.data);
        setUpcomingMatches(formattedUpcoming);
      } else {
        console.warn('Failed to fetch upcoming matches:', upcomingResponse.error);
        setUpcomingMatches(fallbackUpcomingMatches);
      }

      if (previousResponse.success && previousResponse.data) {
        const formattedPrevious = MatchService.convertApiResponseToDashboardFormat(previousResponse.data);
        setPreviousMatches(formattedPrevious);
      } else {
        console.warn('Failed to fetch previous matches:', previousResponse.error);
        setPreviousMatches(fallbackPreviousMatches);
      }

    } catch (error: any) {
      console.error('Error fetching matches:', error);
      setError('Failed to load matches');
      // Use fallback data
      setUpcomingMatches(fallbackUpcomingMatches);
      setPreviousMatches(fallbackPreviousMatches);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Refresh handler
  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchMatches(false);
  };

  // Load data on component mount and when screen comes into focus
  useEffect(() => {
    fetchMatches();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      // Refresh data when screen comes into focus (e.g., after creating a match)
      fetchMatches(false);
    }, [])
  );

  const renderUserProfile = () => (
    <View style={styles.profileCard}>
      <View style={styles.avatar} />
      <Text style={styles.userName}>Dave John</Text>
    </View>
  );

  const renderTabs = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity style={[styles.tab, activeTab === 'upcoming' ? styles.tabActive : {}]} onPress={() => setActiveTab('upcoming')}>
        <Text style={[styles.tabText, activeTab === 'upcoming' ? styles.tabTextActive : styles.tabTextInactive]}>Upcoming</Text>
      </TouchableOpacity>
      <TouchableOpacity style={[styles.tab, activeTab === 'previous' ? styles.tabActive : {}]} onPress={() => setActiveTab('previous')}>
        <Text style={[styles.tabText, activeTab === 'previous' ? styles.tabTextActive : styles.tabTextInactive]}>Previous</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCountdown = () => (
    <View style={styles.countdownSection}>
      <Text style={styles.nextGameText}>Next Game FC v FC1</Text>
      <View style={styles.countdownCard}>
        <View style={styles.countdownItem}><Text style={styles.countdownNumber}>5</Text><Text style={styles.countdownLabel}>Days</Text></View>
        <View style={styles.separator} />
        <View style={styles.countdownItem}><Text style={styles.countdownNumber}>21</Text><Text style={styles.countdownLabel}>Hours</Text></View>
        <View style={styles.separator} />
        <View style={styles.countdownItem}><Text style={styles.countdownNumber}>30</Text><Text style={styles.countdownLabel}>Mins</Text></View>
      </View>
    </View>
  );

  const renderMatchCard = (match: Match, index: number) => (
    <View key={index} style={styles.matchCard}>
      <View style={styles.matchHeader}>
        <View style={styles.matchDivision}>
          <View style={[styles.divisionDot, {backgroundColor: theme.primary}]} />
          <Text style={styles.divisionText} numberOfLines={1}>{match.division}</Text>
        </View>
        <View style={styles.roleBadge}><Text style={styles.roleText}>{match.role}</Text></View>
      </View>
      <TouchableOpacity onPress={() => {
        if (activeTab === 'previous') {
          navigation.navigate('MatchResults' as never);
        } else {
          navigation.navigate('MatchDetail' as never);
        }
      }}>
        <View style={styles.teamsCard}>
          {/* Home Team Column */}
          <View style={styles.teamColumn}>
            <StripedTshirt baseColor={match.homeTeam.kitColor} />
            <Text style={styles.teamName}>{match.homeTeam.name}</Text>
          </View>

          {/* Center Column for Score/Separator */}
          {typeof match.homeScore === 'number' && typeof match.awayScore === 'number' ? (
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreText}>{`${match.homeScore} - ${match.awayScore}`}</Text>
            </View>
          ) : (
            <View style={styles.teamSeparatorContainer}>
                <View style={styles.teamSeparator} />
            </View>
          )}

          {/* Away Team Column */}
          <View style={styles.teamColumn}>
            <StripedTshirt baseColor={match.awayTeam.kitColor} />
            <Text style={styles.teamName}>{match.awayTeam.name}</Text>
          </View>
        </View>
      </TouchableOpacity>
      <View style={styles.matchDetails}>
        <View style={styles.locationSection}>
          <MaterialIcons name="location-on" size={16} color={theme.text} style={{opacity: 0.7}} />
          <Text style={styles.locationText}>{match.location}</Text>
        </View>
        <View style={styles.timeSection}>
          <Text style={styles.dateText}>{match.date}</Text>
          <View style={styles.timeSeparator} />
          <Text style={styles.timeText}>{match.time}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Header
        title="Dashboard"
        rightComponent={
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity onPress={() => navigation.navigate('FilterMatches' as never)} style={{ padding: 4 }}>
              <MaterialIcons name="filter-list" size={24} color={theme.primary} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => navigation.navigate('CreateMatch' as never)} style={styles.addButton}>
              <MaterialIcons name="add" size={24} color={theme.primary} />
            </TouchableOpacity>
          </View>
        }
      />
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={theme.primary}
            colors={[theme.primary]}
          />
        }
      >
        {renderUserProfile()}
        {renderTabs()}

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={styles.loadingText}>Loading matches...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <MaterialIcons name="error-outline" size={48} color={theme.text} style={{ opacity: 0.5 }} />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={() => fetchMatches()}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View>
            {activeTab === 'upcoming' ? (
              <>
                {upcomingMatches.length > 0 && renderCountdown()}
                {upcomingMatches.length > 0 ? (
                  upcomingMatches.map(renderMatchCard)
                ) : (
                  <View style={styles.emptyContainer}>
                    <MaterialIcons name="event" size={48} color={theme.text} style={{ opacity: 0.3 }} />
                    <Text style={styles.emptyText}>No upcoming matches</Text>
                    <Text style={styles.emptySubText}>Create your first match to get started</Text>
                  </View>
                )}
              </>
            ) : (
              <>
                {previousMatches.length > 0 ? (
                  previousMatches.map(renderMatchCard)
                ) : (
                  <View style={styles.emptyContainer}>
                    <MaterialIcons name="history" size={48} color={theme.text} style={{ opacity: 0.3 }} />
                    <Text style={styles.emptyText}>No previous matches</Text>
                    <Text style={styles.emptySubText}>Your match history will appear here</Text>
                  </View>
                )}
              </>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.background },
  addButton: { padding: 4, marginLeft: 16 },
  content: { flex: 1 },
  profileCard: {
    flexDirection: 'row', alignItems: 'center', marginHorizontal: 16,
    marginTop: 12, marginBottom: 8, padding: 12, borderRadius: 10,
    backgroundColor: theme.card,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.05,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  countdownCard: {
    flexDirection: 'row', alignItems: 'center', paddingHorizontal: 20,
    paddingVertical: 16, backgroundColor: theme.card, borderRadius: 10,
    width: width * 0.75, justifyContent: 'space-evenly',
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.05,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  matchCard: {
    marginHorizontal: 16, marginBottom: 12, padding: 12,
    backgroundColor: theme.card, borderRadius: 10,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  avatar: { width: 48, height: 48, borderRadius: 24, backgroundColor: theme.border },
  userName: { marginLeft: 16, fontSize: 18, fontWeight: '700', color: theme.text, flexShrink: 1 },
  tabContainer: {
    flexDirection: 'row', marginHorizontal: 16, marginVertical: 12,
    height: 40, borderRadius: 8, backgroundColor: `${theme.text}12`,
  },
  tab: { flex: 1, alignItems: 'center', justifyContent: 'center', borderRadius: 6, margin: 2 },
  tabActive: { backgroundColor: theme.primary },
  tabText: { fontSize: 14, fontWeight: '600' },
  tabTextActive: { color: '#ffffff' },
  tabTextInactive: { color: theme.text, opacity: 0.7 },
  countdownSection: { alignItems: 'center', marginTop: 4, marginBottom: 16 },
  nextGameText: { fontSize: 14, fontWeight: '600', color: theme.text, marginBottom: 12 },
  countdownItem: { alignItems: 'center', justifyContent: 'center', minWidth: 50 },
  countdownNumber: { fontSize: 24, fontWeight: '700', color: theme.text, lineHeight: 28 },
  countdownLabel: { fontSize: 11, fontWeight: '500', color: theme.text, opacity: 0.7, marginTop: 2 },
  separator: { width: 1, height: 30, backgroundColor: theme.border },
  matchHeader: {
    flexDirection: 'row', alignItems: 'center',
    justifyContent: 'space-between', marginBottom: 8, gap: 8,
  },
  matchDivision: { flexDirection: 'row', alignItems: 'center', flexShrink: 1, overflow: 'hidden' },
  divisionDot: { width: 12, height: 12, borderRadius: 6, marginRight: 8 },
  divisionText: { fontSize: 12, fontWeight: '500', color: theme.text, opacity: 0.7 },
  roleBadge: {
    backgroundColor: theme.background, paddingHorizontal: 12,
    paddingVertical: 6, borderRadius: 6,
  },
  roleText: { fontSize: 12, fontWeight: '600', color: theme.text },
  teamsCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 4,
    backgroundColor: `${theme.text}08`,
    borderRadius: 8,
    marginBottom: 8,
  },
  teamColumn: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  teamName: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.text,
    textAlign: 'center',
    lineHeight: 16,
    marginTop: 6,
  },
  teamSeparatorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    height: 36,
    marginHorizontal: 4,
  },
  teamSeparator: {
    width: 1,
    height: 24,
    backgroundColor: theme.text,
  },
  scoreContainer: {
    backgroundColor: theme.border,
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginHorizontal: 4,
  },
  scoreText: { fontSize: 16, fontWeight: 'bold', color: theme.text },
  matchDetails: {
    flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between',
    padding: 12, backgroundColor: 'transparent',
    borderRadius: 8, borderWidth: 1, borderColor: theme.border1, gap: 16,
  },
  locationSection: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  locationText: { marginLeft: 8, fontSize: 13, fontWeight: '500', color: theme.text, opacity: 0.8, flexShrink: 1 },
  timeSection: { flexDirection: 'row', alignItems: 'center' },
  dateText: { fontSize: 13, fontWeight: '600', color: theme.text },
  timeSeparator: { width: 1, height: 16, backgroundColor: theme.border1, marginHorizontal: 12 },
  timeText: { fontSize: 13, fontWeight: '600', color: theme.text },

  // Loading, error, and empty states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.text,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.text,
    textAlign: 'center',
    opacity: 0.7,
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: theme.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
  },
  emptySubText: {
    marginTop: 8,
    fontSize: 14,
    color: theme.text,
    opacity: 0.6,
    textAlign: 'center',
  },
});

export default UserDashboard;