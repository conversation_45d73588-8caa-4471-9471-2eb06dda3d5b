http://schemas.android.com/apk/res-auto;mobile-app:react-native-edge-to-edge:unspecified;$GRADLE_USER_HOME/caches/8.13/transforms/7f7a252943970c7392e3b1ee0bfe12b1/transformed/out/res/values/values.xml,$GRADLE_USER_HOME/caches/8.13/transforms/7f7a252943970c7392e3b1ee0bfe12b1/transformed/out/res/values-night-v27/values-night-v27.xml,$GRADLE_USER_HOME/caches/8.13/transforms/7f7a252943970c7392e3b1ee0bfe12b1/transformed/out/res/values-night-v8/values-night-v8.xml,$GRADLE_USER_HOME/caches/8.13/transforms/7f7a252943970c7392e3b1ee0bfe12b1/transformed/out/res/values-v27/values-v27.xml,$GRADLE_USER_HOME/caches/8.13/transforms/7f7a252943970c7392e3b1ee0bfe12b1/transformed/out/res/values-v29/values-v29.xml,$GRADLE_USER_HOME/caches/8.13/transforms/7f7a252943970c7392e3b1ee0bfe12b1/transformed/out/res/values-v30/values-v30.xml,+attr:enforceNavigationBarContrast,0,V400020037,4000020073,;boolean:;enforceSystemBarsLightTheme,0,V400030078,3f000300b3,;boolean:;+bool:windowLightSystemBars,0,V4000400b8,32000400e6,;"true";windowLightSystemBars,1,V400020037,3300020066,;"false";windowLightSystemBars,2,V400020037,3300020066,;"false";windowLightSystemBars,3,V400020037,3200020065,;"true";+color:navigationBarColor,0,V4000500eb,360005011d,;"#801b1b1b";navigationBarColor,3,V40003006a,47000300ad,;"@android\:color/transparent";+style:Theme.EdgeToEdge.Material3.Light,0,V4005314b1,6500531512,;DTheme.EdgeToEdge.Material3.Light.Common,;Theme.EdgeToEdge.Material3.Light,3,V40020080f,c0023090d,;DTheme.EdgeToEdge.Material3.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:shortEdges,;Theme.EdgeToEdge.Material3.Light,4,V4002c0c02,c00310da2,;DTheme.EdgeToEdge.Material3.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material3.Light,5,V4002c0be6,c00310d82,;DTheme.EdgeToEdge.Material3.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:always,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material2.Light,0,V40027094a,65002709ab,;DTheme.EdgeToEdge.Material2.Light.Common,;Theme.EdgeToEdge.Material2.Light,3,V4001003bb,c001304b9,;DTheme.EdgeToEdge.Material2.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:shortEdges,;Theme.EdgeToEdge.Material2.Light,4,V400140526,c001906c6,;DTheme.EdgeToEdge.Material2.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material2.Light,5,V40014051a,c001906b6,;DTheme.EdgeToEdge.Material2.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:always,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material2,0,V4001c066f,62001c06cd,;DTheme.EdgeToEdge.Material2.DayNight.Common,;Theme.EdgeToEdge.Material2,3,V4000c02a4,c000f03b6,;DTheme.EdgeToEdge.Material2.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:shortEdges,;Theme.EdgeToEdge.Material2,4,V4000e036d,c00130521,;DTheme.EdgeToEdge.Material2.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material2,5,V4000e0365,c00130515,;DTheme.EdgeToEdge.Material2.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:always,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material3,0,V400320c0a,6200320c68,;DTheme.EdgeToEdge.Material3.DayNight.Common,;Theme.EdgeToEdge.Material3,3,V4001404be,c001705d0,;DTheme.EdgeToEdge.Material3.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:shortEdges,;Theme.EdgeToEdge.Material3,4,V4001a06cb,c001f087f,;DTheme.EdgeToEdge.Material3.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material3,5,V4001a06bb,c001f086b,;DTheme.EdgeToEdge.Material3.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:always,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material2.DayNight.Common,0,V4001d06d2,c00260945,;DTheme.MaterialComponents.DayNight.NoActionBar,enforceNavigationBarContrast:true,enforceSystemBarsLightTheme:false,android\:windowDrawsSystemBarBackgrounds:true,android\:fitsSystemWindows:false,android\:navigationBarColor:@color/navigationBarColor,android\:statusBarColor:@android\:color/transparent,android\:windowLightStatusBar:@bool/windowLightSystemBars,;Theme.EdgeToEdge.Material3.Dynamic,0,V4003d0edc,72003d0f4a,;DTheme.EdgeToEdge.Material3.Dynamic.DayNight.Common,;Theme.EdgeToEdge.Material3.Dynamic,3,V4001805d5,c001b06f7,;DTheme.EdgeToEdge.Material3.Dynamic.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:shortEdges,;Theme.EdgeToEdge.Material3.Dynamic,4,V400200884,c00250a48,;DTheme.EdgeToEdge.Material3.Dynamic.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material3.Dynamic,5,V400200870,c00250a30,;DTheme.EdgeToEdge.Material3.Dynamic.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:always,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common,0,V4003e0f4f,c004711cf,;DTheme.Material3.DynamicColors.DayNight.NoActionBar,enforceNavigationBarContrast:true,enforceSystemBarsLightTheme:false,android\:windowDrawsSystemBarBackgrounds:true,android\:fitsSystemWindows:false,android\:navigationBarColor:@color/navigationBarColor,android\:statusBarColor:@android\:color/transparent,android\:windowLightStatusBar:@bool/windowLightSystemBars,;Theme.EdgeToEdge.Material3.Dynamic.Light,0,V4004811d4,7500481245,;DTheme.EdgeToEdge.Material3.Dynamic.Light.Common,;Theme.EdgeToEdge.Material3.Dynamic.Light,3,V4001c06fc,c001f080a,;DTheme.EdgeToEdge.Material3.Dynamic.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:shortEdges,;Theme.EdgeToEdge.Material3.Dynamic.Light,4,V400260a4d,c002b0bfd,;DTheme.EdgeToEdge.Material3.Dynamic.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material3.Dynamic.Light,5,V400260a35,c002b0be1,;DTheme.EdgeToEdge.Material3.Dynamic.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:always,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Material3.Dynamic.Light.Common,0,V40049124a,c005214ac,;DTheme.Material3.DynamicColors.Light.NoActionBar,enforceNavigationBarContrast:true,enforceSystemBarsLightTheme:true,android\:windowDrawsSystemBarBackgrounds:true,android\:fitsSystemWindows:false,android\:navigationBarColor:@color/navigationBarColor,android\:statusBarColor:@android\:color/transparent,android\:windowLightStatusBar:true,;Theme.EdgeToEdge.Material2.Light.Common,0,V4002809b0,c00310c05,;DTheme.MaterialComponents.Light.NoActionBar,enforceNavigationBarContrast:true,enforceSystemBarsLightTheme:true,android\:windowDrawsSystemBarBackgrounds:true,android\:fitsSystemWindows:false,android\:navigationBarColor:@color/navigationBarColor,android\:statusBarColor:@android\:color/transparent,android\:windowLightStatusBar:true,;Theme.EdgeToEdge.Material3.DayNight.Common,0,V400330c6d,c003c0ed7,;DTheme.Material3.DayNight.NoActionBar,enforceNavigationBarContrast:true,enforceSystemBarsLightTheme:false,android\:windowDrawsSystemBarBackgrounds:true,android\:fitsSystemWindows:false,android\:navigationBarColor:@color/navigationBarColor,android\:statusBarColor:@android\:color/transparent,android\:windowLightStatusBar:@bool/windowLightSystemBars,;Theme.EdgeToEdge.Material3.Light.Common,0,V400541517,c005d1763,;DTheme.Material3.Light.NoActionBar,enforceNavigationBarContrast:true,enforceSystemBarsLightTheme:true,android\:windowDrawsSystemBarBackgrounds:true,android\:fitsSystemWindows:false,android\:navigationBarColor:@color/navigationBarColor,android\:statusBarColor:@android\:color/transparent,android\:windowLightStatusBar:true,;Theme.EdgeToEdge,0,V400060122,4e0006016c,;DTheme.EdgeToEdge.DayNight.Common,;Theme.EdgeToEdge,3,V4000400b2,c000701b0,;DTheme.EdgeToEdge.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:shortEdges,;Theme.EdgeToEdge,4,V400020037,c000701d7,;DTheme.EdgeToEdge.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge,5,V400020037,c000701d3,;DTheme.EdgeToEdge.DayNight.Common,android\:windowLightNavigationBar:@bool/windowLightSystemBars,android\:windowLayoutInDisplayCutoutMode:always,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Light,0,V4001103d6,5100110423,;DTheme.EdgeToEdge.Light.Common,;Theme.EdgeToEdge.Light,3,V4000801b5,c000b029f,;DTheme.EdgeToEdge.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:shortEdges,;Theme.EdgeToEdge.Light,4,V4000801dc,c000d0368,;DTheme.EdgeToEdge.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:shortEdges,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.Light,5,V4000801d8,c000d0360,;DTheme.EdgeToEdge.Light.Common,android\:windowLightNavigationBar:true,android\:windowLayoutInDisplayCutoutMode:always,android\:enforceStatusBarContrast:false,android\:enforceNavigationBarContrast:?enforceNavigationBarContrast,;Theme.EdgeToEdge.DayNight.Common,0,V400070171,c001003d1,;DTheme.AppCompat.DayNight.NoActionBar,enforceNavigationBarContrast:true,enforceSystemBarsLightTheme:false,android\:windowDrawsSystemBarBackgrounds:true,android\:fitsSystemWindows:false,android\:navigationBarColor:@color/navigationBarColor,android\:statusBarColor:@android\:color/transparent,android\:windowLightStatusBar:@bool/windowLightSystemBars,;Theme.EdgeToEdge.Light.Common,0,V400120428,c001b066a,;DTheme.AppCompat.Light.NoActionBar,enforceNavigationBarContrast:true,enforceSystemBarsLightTheme:true,android\:windowDrawsSystemBarBackgrounds:true,android\:fitsSystemWindows:false,android\:navigationBarColor:@color/navigationBarColor,android\:statusBarColor:@android\:color/transparent,android\:windowLightStatusBar:true,;