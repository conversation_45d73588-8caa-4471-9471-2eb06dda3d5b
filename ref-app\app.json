{"expo": {"name": "mobile-app", "slug": "mobile-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": false, "scheme": "com.refrate.refereetracker", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#202121"}, "android": {"package": "com.refrate.refereetracker", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#202121"}, "edgeToEdgeEnabled": true, "backgroundColor": "#FFFFFF", "backgroundColorDark": "#181818"}, "ios": {"bundleIdentifier": "com.refrate.refereetracker", "supportsTablet": true, "backgroundColor": "#202121"}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "fc356406-fa6c-49f1-b944-4cfb129a9489"}}, "plugins": [["@react-native-google-signin/google-signin", {"reservedClientId": "com.googleusercontent.apps.723844449983-692lcotd6v1hdntqhduf9jqvqak1to7o", "iosUrlScheme": "com.googleusercontent.apps.723844449983-692lcotd6v1hdntqhduf9jqvqak1to7o"}], ["react-native-fbsdk-next", {"appID": "651727944558014", "clientToken": "480507d9ec462e5f00467a9437d5e447", "displayName": "mobile-app", "scheme": "fb651727944558014", "advertiserIDCollectionEnabled": false, "autoLogAppEventsEnabled": false, "isAutoInitEnabled": true, "iosUserTrackingPermission": "This identifier will be used to deliver personalized ads to you."}], ["expo-dev-client", {"launchMode": "most-recent", "addGeneratedScheme": true}]]}}