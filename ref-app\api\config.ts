// API Configuration
export const API_CONFIG = {
  // Use your local IP address or localhost
  // For Android emulator, use ********
  // For iOS simulator, use localhost
  // For physical device, use your computer's IP address
  BASE_URL: __DEV__
    ? 'http://********:3000'  // Development - Android emulator
    : 'https://your-production-api.com', // Production
  
  TIMEOUT: 10000, // 10 seconds
  
  ENDPOINTS: {
    // Auth endpoints
    AUTH: {
      VERIFY_TOKEN: '/auth/verify-token',
    },
    
    // Match endpoints
    MATCHES: {
      BASE: '/matches',
      UPCOMING: '/matches/upcoming',
      PREVIOUS: '/matches/previous',
      BY_ID: (id: string) => `/matches/${id}`,
      UPDATE_STATUS: (id: string) => `/matches/${id}/status`,
    },
  }
};

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message: string;
}

// Match types for API
export interface CreateMatchRequest {
  // Basic match info
  competition: string;
  venue: string;
  matchDate: string;
  officialRole?: string;

  // Home team
  homeTeamName: string;
  homeTeamShortName?: string;
  homeKitBase?: string;
  homeKitStripe?: string;

  // Away team
  awayTeamName: string;
  awayTeamShortName?: string;
  awayKitBase?: string;
  awayKitStripe?: string;

  // Match format
  teamSize?: number;
  benchSize?: number;
  numberOfPeriods?: number;
  periodLengths?: number[];
  halfTime?: number;

  // Extra time settings
  extraTime?: boolean;
  extraTimeLengths?: [number, number];

  // Substitution rules
  subOpportunities?: boolean;
  subOpportunitiesAllowance?: number;
  extraTimeSubOpportunities?: boolean;
  extraTimeSubOpportunitiesAllowance?: number;

  // Other rules
  penalties?: boolean;
  misconductCode?: string;
  temporaryDismissals?: boolean;
  temporaryDismissalsTime?: number;

  // Injury time
  injuryTimeAllowance?: boolean;
  injuryTimeSubs?: number;
  injuryTimeSanctions?: number;
  injuryTimeGoals?: number;

  // Match officials
  matchOfficials?: Array<{ role: string; name: string }>;

  // Earnings
  fees?: string;
  expenses?: string;
  mileage?: string;
  travelTime?: string;

  // Notes
  notes?: string;

  // Template info
  templateName?: string;
}

export interface MatchResponse {
  id: string;
  competition: string;
  venue: string;
  matchDate: string;
  officialRole: string;
  
  homeTeamName: string;
  homeTeamShortName?: string;
  homeKitBase: string;
  homeKitStripe: string;
  
  awayTeamName: string;
  awayTeamShortName?: string;
  awayKitBase: string;
  awayKitStripe: string;
  
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  createdAt: string;
  updatedAt: string;
  
  referee: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}
