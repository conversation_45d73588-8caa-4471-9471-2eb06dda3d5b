import React, { useState } from 'react';
import { 
  View, 
  StyleSheet, 
  ScrollView, 
  Text, 
  TextInput,
  TouchableOpacity,
  Alert 
} from 'react-native';
import Header from '../components/Header';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct

type Question = {
  id: number;
  text: string;
  isEditing: boolean;
};

const initialQuestions: Question[] = [
  { id: 1, text: 'How was the overall team performance?', isEditing: false },
  { id: 2, text: 'Did the team show good sportsmanship?', isEditing: false },
  { id: 3, text: 'What was the most significant moment of the match?', isEditing: false },
  { id: 4, text: 'Which player had the biggest impact?', isEditing: false },
  { id: 5, text: 'What could the team improve for the next match?', isEditing: false },
];

const EvaluateQuestions = () => {
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);
  const [questions, setQuestions] = useState<Question[]>(initialQuestions);

  const handleEditToggle = (id: number) => {
    setQuestions(currentQuestions =>
      currentQuestions.map(q =>
        q.id === id ? { ...q, isEditing: !q.isEditing } : q
      )
    );
  };

  const handleTextChange = (newText: string, id: number) => {
    setQuestions(currentQuestions =>
      currentQuestions.map(q =>
        q.id === id ? { ...q, text: newText } : q
      )
    );
  };
  
  const handleSaveAll = () => {
    console.log('Saving all questions:', questions.map(q => q.text));
    
    setQuestions(currentQuestions =>
      currentQuestions.map(q => ({ ...q, isEditing: false }))
    );

    Alert.alert('Success', 'All questions have been saved!');
  };

  return (
    <View style={styles.container}>
      <Header title="Evaluate Questions" showBackButton={true} />
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.infoText}>
          Edit the questions below that will be used for match evaluation.
        </Text>
        
        {questions.map((question) => (
          <View key={question.id} style={styles.questionCard}>
            <View style={styles.questionContent}>
              {question.isEditing ? (
                <TextInput
                  style={styles.input}
                  value={question.text}
                  onChangeText={(newText) => handleTextChange(newText, question.id)}
                  autoFocus={true}
                  multiline={true}
                />
              ) : (
                <Text style={styles.questionText}>{question.text}</Text>
              )}
            </View>
            <TouchableOpacity 
              style={styles.editButton} 
              onPress={() => handleEditToggle(question.id)}
            >
              <MaterialIcons 
                name={question.isEditing ? "save" : "edit"} 
                size={18}
                color={theme.text} 
              />
            </TouchableOpacity>
          </View>
        ))}
        
        <TouchableOpacity style={styles.saveAllButton} onPress={handleSaveAll}>
          <Text style={styles.saveAllButtonText}>Save All Questions</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  scrollContent: {
    padding: 12,
  },
  infoText: {
    fontSize: 13,
    color: theme.text,
    opacity: 0.7,
    marginBottom: 12,
    textAlign: 'center',
  },
  questionCard: {
    backgroundColor: theme.card,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // --- Conditional shadow and border ---
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.08,
    shadowRadius: 1.5,
    elevation: isDarkMode ? 0 : 1,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  questionContent: {
    flex: 1,
    marginRight: 8,
  },
  questionText: {
    fontSize: 14,
    color: theme.text,
    lineHeight: 20,
  },
  input: {
    fontSize: 14,
    color: theme.text,
    lineHeight: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.primary,
    paddingBottom: 2,
  },
  editButton: {
    padding: 5,
    borderRadius: 20,
    backgroundColor: theme.background,
  },
  saveAllButton: {
    backgroundColor: theme.primary, 
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  saveAllButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: 'bold',
  },
});

export default EvaluateQuestions;