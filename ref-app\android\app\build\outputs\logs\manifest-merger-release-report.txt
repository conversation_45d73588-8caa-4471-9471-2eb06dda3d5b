-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:1:1-47:12
INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:1:1-47:12
INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:1:1-47:12
INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:1:1-47:12
MERGED from [:react-native-google-signin_google-signin] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:1-50:12
MERGED from [:react-native-linear-gradient] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3cb4b8a3e8b83441d60f6c1f4578060\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\f391aef30b5f82d91498010af59cbeca\transformed\expo.modules.systemui-5.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7eb301ebab3935e1c8a7c29a974d3ae\transformed\react-android-0.79.4-release\AndroidManifest.xml:2:1-12:12
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c087da39ea1b23ed338ff1b69a2a8311\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec572916ea1f129e0942696847a1448\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4f1d68817d697725d9ad4b19dc40c62\transformed\facebook-login-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ba2dea9ba719ef228752815abd6740\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b3781c4089eba258b09d46b1a519dc4\transformed\facebook-share-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:9:1-42:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f64dc84a821f94336e410d15a520d5cf\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0756b559140f80768a7c93814a53d41b\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\a12f9d829a1cd9cbf62215df52715ccd\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5b1793cdf5dbf1144d825bf183ca08\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-33:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8ce9a1be2d3cd86a208e483c0813531\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1bf76f9dc3a11f29be9edef53e9c03c\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e259728d88428d0a6fd4fb3d2f819b3\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5972ddf3f9cbc9412d6a8e83292e1a6d\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a4718908bdbe7123486239e33dd3abf\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2efc5fe18ad62bb62b3900ca04e3af20\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6e7a1c501830cd533d157e9b41a5335\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2d834d46678a12efa47d1f23b07a9e8\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2897a7ff40e476104e5ab7f783061025\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\625591389eccd7ec5d724d7c036d398c\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\500e0f19ddb3e6600be928adcccc0bb1\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7133ea62f60e2d183b5e312dbc30a8d5\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf2a3aa581241838cbf58851f10edb22\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f469c22dbb07422dede643d5a039dcc\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17ac95e9c984766d207c191df0f70aeb\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6cf3e9bc2c76bfab81fd0cfb5075cf4\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\edd9b297e45b401c6bb55a3abcbde149\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bed25775f45658e90985fa841f93676f\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0082b64a005a6847fda5318a3bb8e7d8\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d74b09dce8f6d387f3c2635b10a9e7\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6aa867a131bf976b735a3ed59884142\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7b6583d7cb19f7e4a72e243f5302f34\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d3460ec15ddb0a561b4f2d158f0ce1c\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81478b71232bd548e33d1a1749fb9b7a\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d537a3f89dbc152250982567f41e363\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3a5c9efc7eb0c5d1876cea789b1eeb0\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-constants] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\72d1668410375b774c3240a00d9463f5\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\085c40239db3e005440ea53bd6557a31\transformed\expo.modules.crypto-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f22d7661885c24ffe3b9ea3b76786d74\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\483de06e67f7cfa61d4d4a967beed3c7\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c35f100852bac5d752ade46720091b9\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d52c62331fda403e7b59eb4e693e28be\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6dc552d30d651ad18fb9655d38bfcc3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c52fddb3f71df7c52c6d2210bdbf0f\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5f356aedc74e44a86f4294ab019421b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7bc0d3483dd2faa77540c8e42cad49c\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbcb18e87b04f5f882cbe197edb0007a\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\28076fe4f56d6303833241a337b6a5f8\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:9:1-53:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\540a2f03ddcc6a751649be6050a9c6d1\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d709a894bfe1beef7a6bcd438049cfc6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df0d9a6769b6bb3e297ac6f60c9777d2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\890f8c54f25e7e62ce290c19c23f34b2\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a19d447f99dacc987e9045d6bd5cdca\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ae8c9521be24c830756d5a3e3c2bb39\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ebe30aeca87bb2621bf1e5abb9d628c\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa90bc2a178b0b5f87435f59781d1b83\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70d7f575b60629c02bee4d2518844cc0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fbcaf4b95e6e5de7e2154f7ed18be78\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\029e2af128490e615d059bebc021f478\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9194d47ce755d1ac815ee8da7b1e34ff\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d35d4966db12ff4966be000f36c2c1d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c184311a2cea37320ef6afbc4a0aa502\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9814bd31aa17508a9e10bfcf0775a1\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5671a8d340f2eb23a40634609ace1d32\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96427a7a76dd31fe7b081824e489e8be\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\55c65349e9f76a371a924d8b223cc8c7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7876c15d01538d67cc2c4103d1dcafe1\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca79320bf5be63e572f5185dbdb89374\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\076e7c8d86f4ae165701d57613b55ad7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c452e07798d5e01f66963b4329b14cc\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7470730cec1e0826f1d938b05924fd06\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4998a702a63fa929eca66b6b57624d0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5ef8d394a6c24184eafbdfe860a06e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2984cc02f72a737058cfeca27fb04878\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a97664ae2368e45ad054d43ee02ac19\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf251b680b4c0081841a858beaef439f\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a110528b690b862fb4278e561fbf4930\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b172e2b00bfe9a72e271fb125e5750a\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4bb358b6c1db4d4bfc8fab82b7f208b\transformed\hermes-android-0.79.4-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\852a4e7a5f7d3c1d9e1bc1b8314a88c0\transformed\viewbinding-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93b838198568727d6d4c5905a2759c53\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e78ab6e66fc9e7893b1d10deceb25bd2\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ca19e7979f29ab7958a2f79eb3d738\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c4431013e849e78fe14e7940f18c7ee\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c57e05f42478481098488e1a1964e4ec\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c845dd94f68af6ed1cea7bb44ff832e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4034b4b1ab8419396693267014837625\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1450e4ef69b40f313f4440b3ddafbeb\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fa947bf67ce55d61e42d2703565d53\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f1a14ade7cb4bc06475ad48c27e26fd\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b8d9f7f8cd67980f1dd3adb74bef188\transformed\installreferrer-1.0\AndroidManifest.xml:2:1-13:12
	package
		INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:2:3-64
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-67
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:2:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:3:3-77
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-80
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:3:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:4:3-75
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:4:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:5:3-63
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:5:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:6:3-78
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:6:20-76
queries
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:7:3-13:13
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-18:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:8:5-12:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:7-58
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:9:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:7-67
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:10:17-65
data
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:7-37
	android:scheme
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:11:13-35
application
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:3-46:17
INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:3-46:17
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:5-48:19
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:5-48:19
MERGED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c087da39ea1b23ed338ff1b69a2a8311\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c087da39ea1b23ed338ff1b69a2a8311\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4f1d68817d697725d9ad4b19dc40c62\transformed\facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4f1d68817d697725d9ad4b19dc40c62\transformed\facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ba2dea9ba719ef228752815abd6740\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ba2dea9ba719ef228752815abd6740\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b3781c4089eba258b09d46b1a519dc4\transformed\facebook-share-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b3781c4089eba258b09d46b1a519dc4\transformed\facebook-share-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5b1793cdf5dbf1144d825bf183ca08\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5b1793cdf5dbf1144d825bf183ca08\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:5-31:19
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbcb18e87b04f5f882cbe197edb0007a\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbcb18e87b04f5f882cbe197edb0007a\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\28076fe4f56d6303833241a337b6a5f8\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\28076fe4f56d6303833241a337b6a5f8\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ae8c9521be24c830756d5a3e3c2bb39\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ae8c9521be24c830756d5a3e3c2bb39\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b8d9f7f8cd67980f1dd3adb74bef188\transformed\installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b8d9f7f8cd67980f1dd3adb74bef188\transformed\installreferrer-1.0\AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:221-247
	android:label
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:48-80
	android:roundIcon
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:116-161
	android:icon
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:81-115
	android:allowBackup
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:162-188
	android:theme
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:189-220
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:14:16-47
meta-data#com.facebook.sdk.AdvertiserIDCollectionEnabled
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:15:5-101
	android:value
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:15:78-99
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:15:16-77
meta-data#com.facebook.sdk.ApplicationId
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:16:5-103
	android:value
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:16:62-101
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:16:16-61
meta-data#com.facebook.sdk.ApplicationName
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:17:5-92
	android:value
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:17:64-90
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:17:16-63
meta-data#com.facebook.sdk.AutoInitEnabled
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:18:5-86
	android:value
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:18:64-84
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:18:16-63
meta-data#com.facebook.sdk.AutoLogAppEventsEnabled
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:19:5-95
	android:value
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:19:72-93
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:19:16-71
meta-data#com.facebook.sdk.ClientToken
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:20:5-107
	android:value
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:20:60-105
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:20:16-59
meta-data#expo.modules.updates.ENABLED
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:21:5-83
	android:value
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:21:60-81
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:21:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:22:5-105
	android:value
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:22:81-103
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:22:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:23:5-99
	android:value
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:23:80-97
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:23:16-79
activity#com.refrate.refereetracker.MainActivity
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:5-36:16
	android:screenOrientation
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:280-316
	android:launchMode
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:135-166
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:167-209
	android:exported
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:256-279
	android:configChanges
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:44-134
	android:theme
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:210-255
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:24:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:25:7-28:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:26:9-60
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:26:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:27:9-68
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:27:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.refrate.refereetracker+data:scheme:exp+mobile-app
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:29:7-35:23
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:31:9-67
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:31:19-65
activity#com.facebook.FacebookActivity
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:37:5-178
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
	android:label
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:37:144-176
	android:configChanges
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:37:60-143
	android:theme
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:23:13-63
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:37:15-59
activity#com.facebook.CustomTabActivity
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:38:5-45:16
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
	android:exported
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:38:61-84
	android:name
		ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:38:15-60
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:@string/fb_login_protocol_scheme
ADDED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml:39:7-44:23
uses-sdk
INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml
MERGED from [:react-native-google-signin_google-signin] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-signin_google-signin] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:5-44
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:5-44
MERGED from [:react-native-linear-gradient] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3cb4b8a3e8b83441d60f6c1f4578060\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3cb4b8a3e8b83441d60f6c1f4578060\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\f391aef30b5f82d91498010af59cbeca\transformed\expo.modules.systemui-5.0.10\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\f391aef30b5f82d91498010af59cbeca\transformed\expo.modules.systemui-5.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7eb301ebab3935e1c8a7c29a974d3ae\transformed\react-android-0.79.4-release\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7eb301ebab3935e1c8a7c29a974d3ae\transformed\react-android-0.79.4-release\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c087da39ea1b23ed338ff1b69a2a8311\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c087da39ea1b23ed338ff1b69a2a8311\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6550f91160cccf4761958ba318c6d009\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec572916ea1f129e0942696847a1448\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec572916ea1f129e0942696847a1448\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4f1d68817d697725d9ad4b19dc40c62\transformed\facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4f1d68817d697725d9ad4b19dc40c62\transformed\facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ba2dea9ba719ef228752815abd6740\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ba2dea9ba719ef228752815abd6740\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b3781c4089eba258b09d46b1a519dc4\transformed\facebook-share-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b3781c4089eba258b09d46b1a519dc4\transformed\facebook-share-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c26aaa9d43f6c32d0679ac0a055837c8\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f64dc84a821f94336e410d15a520d5cf\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f64dc84a821f94336e410d15a520d5cf\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0756b559140f80768a7c93814a53d41b\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0756b559140f80768a7c93814a53d41b\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\a12f9d829a1cd9cbf62215df52715ccd\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\a12f9d829a1cd9cbf62215df52715ccd\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\abad37239e5c0c5eaeb2da98a84efcc8\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5b1793cdf5dbf1144d825bf183ca08\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba5b1793cdf5dbf1144d825bf183ca08\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\977dd9b6a7bcaf2e943a6c10682b187d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8ce9a1be2d3cd86a208e483c0813531\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8ce9a1be2d3cd86a208e483c0813531\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1bf76f9dc3a11f29be9edef53e9c03c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1bf76f9dc3a11f29be9edef53e9c03c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e259728d88428d0a6fd4fb3d2f819b3\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e259728d88428d0a6fd4fb3d2f819b3\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5972ddf3f9cbc9412d6a8e83292e1a6d\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5972ddf3f9cbc9412d6a8e83292e1a6d\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a4718908bdbe7123486239e33dd3abf\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a4718908bdbe7123486239e33dd3abf\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2efc5fe18ad62bb62b3900ca04e3af20\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2efc5fe18ad62bb62b3900ca04e3af20\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6e7a1c501830cd533d157e9b41a5335\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6e7a1c501830cd533d157e9b41a5335\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2d834d46678a12efa47d1f23b07a9e8\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2d834d46678a12efa47d1f23b07a9e8\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2897a7ff40e476104e5ab7f783061025\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2897a7ff40e476104e5ab7f783061025\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\625591389eccd7ec5d724d7c036d398c\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\625591389eccd7ec5d724d7c036d398c\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\500e0f19ddb3e6600be928adcccc0bb1\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\500e0f19ddb3e6600be928adcccc0bb1\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7133ea62f60e2d183b5e312dbc30a8d5\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7133ea62f60e2d183b5e312dbc30a8d5\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf2a3aa581241838cbf58851f10edb22\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf2a3aa581241838cbf58851f10edb22\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f469c22dbb07422dede643d5a039dcc\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f469c22dbb07422dede643d5a039dcc\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17ac95e9c984766d207c191df0f70aeb\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\17ac95e9c984766d207c191df0f70aeb\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6cf3e9bc2c76bfab81fd0cfb5075cf4\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6cf3e9bc2c76bfab81fd0cfb5075cf4\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\edd9b297e45b401c6bb55a3abcbde149\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\edd9b297e45b401c6bb55a3abcbde149\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bed25775f45658e90985fa841f93676f\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bed25775f45658e90985fa841f93676f\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0082b64a005a6847fda5318a3bb8e7d8\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0082b64a005a6847fda5318a3bb8e7d8\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d74b09dce8f6d387f3c2635b10a9e7\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87d74b09dce8f6d387f3c2635b10a9e7\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6aa867a131bf976b735a3ed59884142\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6aa867a131bf976b735a3ed59884142\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7b6583d7cb19f7e4a72e243f5302f34\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7b6583d7cb19f7e4a72e243f5302f34\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d3460ec15ddb0a561b4f2d158f0ce1c\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d3460ec15ddb0a561b4f2d158f0ce1c\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81478b71232bd548e33d1a1749fb9b7a\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81478b71232bd548e33d1a1749fb9b7a\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d537a3f89dbc152250982567f41e363\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d537a3f89dbc152250982567f41e363\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3a5c9efc7eb0c5d1876cea789b1eeb0\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e3a5c9efc7eb0c5d1876cea789b1eeb0\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\72d1668410375b774c3240a00d9463f5\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\72d1668410375b774c3240a00d9463f5\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\085c40239db3e005440ea53bd6557a31\transformed\expo.modules.crypto-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\085c40239db3e005440ea53bd6557a31\transformed\expo.modules.crypto-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f22d7661885c24ffe3b9ea3b76786d74\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f22d7661885c24ffe3b9ea3b76786d74\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\483de06e67f7cfa61d4d4a967beed3c7\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\483de06e67f7cfa61d4d4a967beed3c7\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c35f100852bac5d752ade46720091b9\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c35f100852bac5d752ade46720091b9\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d52c62331fda403e7b59eb4e693e28be\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d52c62331fda403e7b59eb4e693e28be\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6dc552d30d651ad18fb9655d38bfcc3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6dc552d30d651ad18fb9655d38bfcc3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c52fddb3f71df7c52c6d2210bdbf0f\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7c52fddb3f71df7c52c6d2210bdbf0f\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5f356aedc74e44a86f4294ab019421b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5f356aedc74e44a86f4294ab019421b\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7bc0d3483dd2faa77540c8e42cad49c\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7bc0d3483dd2faa77540c8e42cad49c\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbcb18e87b04f5f882cbe197edb0007a\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbcb18e87b04f5f882cbe197edb0007a\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\28076fe4f56d6303833241a337b6a5f8\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\28076fe4f56d6303833241a337b6a5f8\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\540a2f03ddcc6a751649be6050a9c6d1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\540a2f03ddcc6a751649be6050a9c6d1\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d709a894bfe1beef7a6bcd438049cfc6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d709a894bfe1beef7a6bcd438049cfc6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df0d9a6769b6bb3e297ac6f60c9777d2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\df0d9a6769b6bb3e297ac6f60c9777d2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\890f8c54f25e7e62ce290c19c23f34b2\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\890f8c54f25e7e62ce290c19c23f34b2\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a19d447f99dacc987e9045d6bd5cdca\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a19d447f99dacc987e9045d6bd5cdca\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ae8c9521be24c830756d5a3e3c2bb39\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ae8c9521be24c830756d5a3e3c2bb39\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ebe30aeca87bb2621bf1e5abb9d628c\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ebe30aeca87bb2621bf1e5abb9d628c\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa90bc2a178b0b5f87435f59781d1b83\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa90bc2a178b0b5f87435f59781d1b83\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70d7f575b60629c02bee4d2518844cc0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\70d7f575b60629c02bee4d2518844cc0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fbcaf4b95e6e5de7e2154f7ed18be78\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fbcaf4b95e6e5de7e2154f7ed18be78\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\029e2af128490e615d059bebc021f478\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\029e2af128490e615d059bebc021f478\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9194d47ce755d1ac815ee8da7b1e34ff\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9194d47ce755d1ac815ee8da7b1e34ff\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d35d4966db12ff4966be000f36c2c1d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d35d4966db12ff4966be000f36c2c1d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c184311a2cea37320ef6afbc4a0aa502\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c184311a2cea37320ef6afbc4a0aa502\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9814bd31aa17508a9e10bfcf0775a1\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9814bd31aa17508a9e10bfcf0775a1\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5671a8d340f2eb23a40634609ace1d32\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5671a8d340f2eb23a40634609ace1d32\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96427a7a76dd31fe7b081824e489e8be\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96427a7a76dd31fe7b081824e489e8be\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\55c65349e9f76a371a924d8b223cc8c7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\55c65349e9f76a371a924d8b223cc8c7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7876c15d01538d67cc2c4103d1dcafe1\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7876c15d01538d67cc2c4103d1dcafe1\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca79320bf5be63e572f5185dbdb89374\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca79320bf5be63e572f5185dbdb89374\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\076e7c8d86f4ae165701d57613b55ad7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\076e7c8d86f4ae165701d57613b55ad7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c452e07798d5e01f66963b4329b14cc\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c452e07798d5e01f66963b4329b14cc\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7470730cec1e0826f1d938b05924fd06\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7470730cec1e0826f1d938b05924fd06\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4998a702a63fa929eca66b6b57624d0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4998a702a63fa929eca66b6b57624d0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5ef8d394a6c24184eafbdfe860a06e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d5ef8d394a6c24184eafbdfe860a06e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2984cc02f72a737058cfeca27fb04878\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2984cc02f72a737058cfeca27fb04878\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a97664ae2368e45ad054d43ee02ac19\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a97664ae2368e45ad054d43ee02ac19\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf251b680b4c0081841a858beaef439f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf251b680b4c0081841a858beaef439f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a110528b690b862fb4278e561fbf4930\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a110528b690b862fb4278e561fbf4930\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b172e2b00bfe9a72e271fb125e5750a\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b172e2b00bfe9a72e271fb125e5750a\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4bb358b6c1db4d4bfc8fab82b7f208b\transformed\hermes-android-0.79.4-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4bb358b6c1db4d4bfc8fab82b7f208b\transformed\hermes-android-0.79.4-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\852a4e7a5f7d3c1d9e1bc1b8314a88c0\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\852a4e7a5f7d3c1d9e1bc1b8314a88c0\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93b838198568727d6d4c5905a2759c53\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93b838198568727d6d4c5905a2759c53\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e78ab6e66fc9e7893b1d10deceb25bd2\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e78ab6e66fc9e7893b1d10deceb25bd2\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ca19e7979f29ab7958a2f79eb3d738\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4ca19e7979f29ab7958a2f79eb3d738\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93460c39b0e50e750acaa8985b23a15a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c4431013e849e78fe14e7940f18c7ee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c4431013e849e78fe14e7940f18c7ee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c57e05f42478481098488e1a1964e4ec\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c57e05f42478481098488e1a1964e4ec\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c845dd94f68af6ed1cea7bb44ff832e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c845dd94f68af6ed1cea7bb44ff832e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4034b4b1ab8419396693267014837625\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4034b4b1ab8419396693267014837625\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1450e4ef69b40f313f4440b3ddafbeb\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1450e4ef69b40f313f4440b3ddafbeb\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fa947bf67ce55d61e42d2703565d53\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fa947bf67ce55d61e42d2703565d53\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f1a14ade7cb4bc06475ad48c27e26fd\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f1a14ade7cb4bc06475ad48c27e26fd\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b8d9f7f8cd67980f1dd3adb74bef188\transformed\installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b8d9f7f8cd67980f1dd3adb74bef188\transformed\installreferrer-1.0\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app\src\main\AndroidManifest.xml
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:9-47:48
	android:resource
		ADDED from [:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:13-55
	tools:replace
		ADDED from [:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-45
	android:name
		ADDED from [:react-native-fbsdk-next] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-65
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2bf7eff7d1b94bf459fad217cdc6a0\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93567390536d42b1a4ca355a60943563\transformed\play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
package#com.facebook.katana
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:16:9-55
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:16:18-52
activity#com.facebook.CustomTabMainActivity
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:24:9-71
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:24:19-68
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.${applicationId}+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.com.refrate.refereetracker+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\49d55924a4505df885e20df61137c8f0\transformed\facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d29e10314c2a4b4c7355c13794550e06\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cd7b7d97291c621d53ca066c44c69e\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-29:70
	android:resource
		ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-67
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\r-pro\mobile-app\ref-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-67
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:14:5-79
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:14:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:16:5-88
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:16:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:17:5-82
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:17:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:18:5-92
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:18:22-89
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:19:5-83
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:19:22-80
provider#com.facebook.internal.FacebookInitProvider
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
	android:authorities
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:34:13-72
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:33:13-70
receiver#com.facebook.CurrentAccessTokenExpirationBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:39:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:38:13-86
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
action#com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:41:17-95
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:41:25-92
receiver#com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:46:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:45:13-118
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
action#com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:48:17-103
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\03b287e4ccf01a993ca0ce5e70ab33fc\transformed\facebook-core-18.0.3\AndroidManifest.xml:48:25-100
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c55689459677533d880d5770c041a5a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e5daabb56009edf84873005126adaeb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.refrate.refereetracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.refrate.refereetracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\97ed3adf425b530d685cdd8470e3d9b5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a62a92cabd87091149241399f3f1367\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fac8de52751272cad5500b9f24d9cd0d\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b8d9f7f8cd67980f1dd3adb74bef188\transformed\installreferrer-1.0\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b8d9f7f8cd67980f1dd3adb74bef188\transformed\installreferrer-1.0\AndroidManifest.xml:9:22-107
