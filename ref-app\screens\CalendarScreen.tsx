import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct

const MONTHS = [
  'JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC',
];

const WEEK_DAYS = ['SAT', 'SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI'];

const generateCalendarGrid = (year: number, month: number) => {
  const firstDayOfMonth = new Date(year, month, 1).getDay();
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  let dayCounter = 1;
  const grid = [];
  const startingDayIndex = (firstDayOfMonth + 1) % 7;

  for (let i = 0; i < 6; i++) {
    const week = [];
    for (let j = 0; j < 7; j++) {
      if ((i === 0 && j < startingDayIndex) || dayCounter > daysInMonth) {
        week.push(null);
      } else {
        week.push(dayCounter++);
      }
    }
    grid.push(week);
    if (dayCounter > daysInMonth) break;
  }
  return grid;
};

const matchesData = ['14-05-2025', '15-05-2025', '18-05-2025'];

const CalendarScreen = () => {
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);

  const [currentDate, setCurrentDate] = useState(new Date(2025, 4, 1));
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  const monthScrollViewRef = useRef<ScrollView>(null);
  const [monthLayouts, setMonthLayouts] = useState<Record<number, { x: number; width: number }>>({});
  const selectedMonthIndex = currentDate.getMonth();
  const calendarGrid = generateCalendarGrid(currentDate.getFullYear(), selectedMonthIndex);

  useEffect(() => {
    if (monthLayouts[selectedMonthIndex]) {
      const { x, width } = monthLayouts[selectedMonthIndex];
      const screenWidth = Dimensions.get('window').width;
      const scrollToX = x - screenWidth / 2 + width / 2;
      monthScrollViewRef.current?.scrollTo({ x: scrollToX, animated: true });
    }
  }, [selectedMonthIndex, monthLayouts]);

  const handleMonthPress = (monthIndex: number) => {
    setCurrentDate(new Date(currentDate.getFullYear(), monthIndex, 1));
  };

  const handleDayPress = (day: number) => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const clickedDate = new Date(year, month, day);
    clickedDate.setHours(0, 0, 0, 0);

    if (!startDate || (startDate && endDate)) {
      setStartDate(clickedDate);
      setEndDate(null);
    } else if (startDate && !endDate) {
      if (clickedDate.getTime() < startDate.getTime()) {
        setEndDate(startDate);
        setStartDate(clickedDate);
      } else {
        setEndDate(clickedDate);
      }
    }
  };


  return (
    <View style={styles.container}>
      <Header title="Calendar" />
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        <View style={styles.profileCard}>
          <View style={styles.profilePic} />
          <Text style={styles.profileName}>Dave John</Text>
        </View>

        <ScrollView
          ref={monthScrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.monthScroll}
        >
          {MONTHS.map((month, index) => (
            <TouchableOpacity
              key={month}
              onLayout={(event) => {
                const { x, width } = event.nativeEvent.layout;
                setMonthLayouts((prev) => ({ ...prev, [index]: { x, width } }));
              }}
              style={[
                styles.monthBtn,
                selectedMonthIndex === index && styles.monthBtnActive,
              ]}
              onPress={() => handleMonthPress(index)}
            >
              <Text style={[styles.monthText, selectedMonthIndex === index && styles.monthTextActive]}>
                {month}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <View style={styles.calendarGrid}>
          <View style={styles.weekRow}>
            {WEEK_DAYS.map((day) => (
              <View key={day} style={styles.weekDayContainer}>
                <Text style={styles.weekDayText}>{day}</Text>
              </View>
            ))}
          </View>
          {calendarGrid.map((week, weekIndex) => (
            <View key={weekIndex} style={styles.daysRow}>
              {week.map((day, dayIndex) => {
                if (day === null) {
                  return (
                    <View key={dayIndex} style={[styles.dayBtn, styles.dayBtnDisabled]} />
                  );
                }
                
                const dayDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                dayDate.setHours(0,0,0,0);

                const isSelected = startDate && endDate && dayDate >= startDate && dayDate <= endDate;
                const isStartDateOnly = startDate && !endDate && dayDate.getTime() === startDate.getTime();
                const isActive = isSelected || isStartDateOnly;

                return (
                  <TouchableOpacity
                    key={dayIndex}
                    style={[
                      styles.dayBtn,
                      isActive && styles.dayBtnActive,
                    ]}
                    disabled={!day}
                    onPress={() => day && handleDayPress(day)}
                  >
                    <Text
                      style={[
                        styles.dayText,
                        isActive && styles.dayTextActive,
                      ]}
                    >
                      {day}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          ))}
        </View>

        <View style={styles.matchesList}>
          <Text style={styles.matchesTitle}>Matches</Text>
          {matchesData.map((match, index) => (
            <View key={index} style={styles.matchItem}>
              <Text style={styles.matchItemText}>{match}</Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.background },
  scrollContent: { paddingHorizontal: 16, paddingTop: 12 },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.card,
    borderRadius: 10,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 3,
  },
  profilePic: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: theme.border,
  },
  profileName: {
    marginLeft: 16,
    fontSize: 18,
    fontWeight: '700',
    color: theme.text,
  },
  monthScroll: { marginBottom: 16 },
  monthBtn: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.card,
    borderRadius: 18,
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.border,
  },
  monthBtnActive: { 
    backgroundColor: theme.primary, 
    borderColor: theme.primary,
  },
  monthText: { 
    fontSize: 14,
    color: theme.text, 
    fontWeight: '600' 
  },
  monthTextActive: { color: '#fff' },
  calendarGrid: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 10,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 3,
  },
  weekRow: { 
    flexDirection: 'row', 
    marginBottom: 8 
  },
  weekDayContainer: {
    flex: 1,
    alignItems: 'center',
  },
  weekDayText: {
    color: theme.text,
    opacity: 0.6,
    fontWeight: '600',
    fontSize: 11,
  },
  daysRow: { flexDirection: 'row', justifyContent: 'space-around', marginBottom: 6 },
  dayBtn: {
    flex: 1,
    margin: 3,
    aspectRatio: 1,
    backgroundColor: 'transparent',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dayBtnActive: { 
    backgroundColor: isDarkMode ? `${theme.primary}3990` : '#e9f4e8', 
    borderColor: theme.primary 
  },
  dayBtnDisabled: { backgroundColor: 'transparent', borderWidth: 0 },
  dayText: { 
    color: theme.text, 
    fontWeight: 'bold', 
    fontSize: 14
  },
  dayTextActive: { color: theme.primary, fontWeight: 'bold' },
  matchesList: { marginTop: 8 },
  matchesTitle: { 
    fontWeight: 'bold', 
    fontSize: 15,
    marginBottom: 8,
    color: theme.text,
  },
  matchItem: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 14,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.05,
    shadowRadius: 1,
    elevation: isDarkMode ? 0 : 2,
  },
  matchItemText: {
    color: theme.text,
  },
});

export default CalendarScreen;