import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpException,
  HttpStatus,
  UseGuards,
  Request
} from '@nestjs/common';
import { MatchesService } from './matches.service';
import { CreateMatchDto } from './dto/create-match.dto';
import { UpdateMatchDto } from './dto/update-match.dto';
import { MatchStatus } from '@prisma/client';

@Controller('matches')
export class MatchesController {
  constructor(private readonly matchesService: MatchesService) {}

  @Post()
  async create(@Body() createMatchDto: CreateMatchDto, @Request() req: any) {
    try {
      // For now, we'll use the default referee we created in seed
      // In production, this should come from the authenticated user
      const refereeId = req.user?.id || 'default-referee-id';
      
      const match = await this.matchesService.create(createMatchDto, refereeId);
      
      return {
        success: true,
        data: match,
        message: 'Match created successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to create match'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get()
  async findAll(@Query('refereeId') refereeId?: string) {
    try {
      const matches = await this.matchesService.findAll(refereeId);
      
      return {
        success: true,
        data: matches,
        message: 'Matches retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve matches'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('upcoming')
  async findUpcoming(@Query('refereeId') refereeId?: string) {
    try {
      const matches = await this.matchesService.findUpcoming(refereeId);
      
      return {
        success: true,
        data: matches,
        message: 'Upcoming matches retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve upcoming matches'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('previous')
  async findPrevious(@Query('refereeId') refereeId?: string) {
    try {
      const matches = await this.matchesService.findPrevious(refereeId);
      
      return {
        success: true,
        data: matches,
        message: 'Previous matches retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve previous matches'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    try {
      const match = await this.matchesService.findOne(id);
      
      return {
        success: true,
        data: match,
        message: 'Match retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve match'
        },
        HttpStatus.NOT_FOUND
      );
    }
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateMatchDto: UpdateMatchDto) {
    try {
      const match = await this.matchesService.update(id, updateMatchDto);
      
      return {
        success: true,
        data: match,
        message: 'Match updated successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to update match'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Patch(':id/status')
  async updateStatus(
    @Param('id') id: string,
    @Body() body: { status: MatchStatus }
  ) {
    try {
      const match = await this.matchesService.updateStatus(id, body.status);
      
      return {
        success: true,
        data: match,
        message: 'Match status updated successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to update match status'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    try {
      const match = await this.matchesService.remove(id);
      
      return {
        success: true,
        data: match,
        message: 'Match deleted successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to delete match'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }
}
