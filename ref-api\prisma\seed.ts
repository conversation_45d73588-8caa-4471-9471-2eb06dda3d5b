import { PrismaClient, UserRole } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create a default referee user
  const defaultReferee = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      displayName: '<PERSON>',
      role: UserRole.REFEREE,
      emailVerified: true,
      provider: 'email'
    }
  });

  console.log('✅ Created default referee:', defaultReferee);

  // Create some sample users for testing
  const assessor = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      displayName: '<PERSON>',
      role: UserRole.ASSESSOR,
      emailVerified: true,
      provider: 'email'
    }
  });

  console.log('✅ Created assessor:', assessor);

  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      displayName: 'Admin User',
      role: UserRole.ADMIN,
      emailVerified: true,
      provider: 'email'
    }
  });

  console.log('✅ Created admin:', admin);

  console.log('🎉 Seeding completed!');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
