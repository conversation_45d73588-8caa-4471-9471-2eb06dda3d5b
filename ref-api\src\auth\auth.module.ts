import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { FirebaseService } from './firebase.service';
import { PrismaService } from '../prisma.service';

@Module({
  providers: [AuthService, FirebaseService, PrismaService],
  controllers: [AuthController],
  exports: [AuthService, FirebaseService],
})
export class AuthModule {}
