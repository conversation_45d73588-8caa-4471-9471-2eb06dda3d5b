// Abridged list for demonstration. In a real app, this might come from an API.
export const countries: string[] = [
    'Argentina',
    'Brazil',
    'Canada',
    'Egypt',
    'France',
    'Germany',
    'India',
    'Japan',
    'Mexico',
    'Morocco',
    'Switzerland',
    'Spain',
    'United Arab Emirates',
    'United Kingdom',
    'United States',
];

// This object maps countries to their specific Football Associations.
// The type `{[key: string]: string[]}` defines an object with string keys and string array values.
export const countryFas: { [key: string]: string[] } = {
    'United Kingdom': [
        'England',
        'Northern Ireland',
        'Scotland',
        'Wales',
    ],
    'Spain': [
        'RFEF',
        'Catalan',
        'Basque',
    ],
};