<lint-module
    format="1"
    dir="C:\Users\<USER>\r-pro\mobile-app\ref-app\android\app"
    name=":app"
    type="APP"
    maven="mobile-app:app:unspecified"
    agpVersion="8.8.2"
    buildFolder="build"
    bootClassPath="C:\Android\Sdk\platforms\android-35\android.jar;C:\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
