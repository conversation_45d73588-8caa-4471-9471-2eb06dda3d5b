import React from 'react';
import { View, Text, StyleSheet, Dimensions, ScrollView } from 'react-native';
import { Svg, Path, Rect, Text as SvgText } from 'react-native-svg';
import { useTheme } from '../../contexts/ThemeContext'; // Ensure this path is correct

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// --- Color Palette for non-theme dependent colors ---
const AppColors = {
  Green: '#068b48',
  Orange: '#fe841a',
};

// --- Type Definitions ---
type IconType = 'referee' | 'assistant' | 'fourthOfficial';
type MatchData = {
    score1: number;
    score2: number;
    teams: string;
    date: string;
};

// --- SVG Icon Components (Now accept a color prop) ---
const RefereeIcon = ({ color }: { color: string }) => ( <Svg height="32" width="32" viewBox="2 2 24 24"><Path d="M12.375 21.375C10.5 21.375 8.90625 20.7188 7.59375 19.4062C6.28125 18.0938 5.625 16.5 5.625 14.625C5.625 14.4187 5.63437 14.2125 5.65312 14.0062C5.67188 13.8 5.7 13.5938 5.7375 13.3875C5.64375 13.425 5.53125 13.4531 5.4 13.4719C5.26875 13.4906 5.15625 13.5 5.0625 13.5C4.275 13.5 3.60938 13.2281 3.06562 12.6844C2.52187 12.1406 2.25 11.475 2.25 10.6875C2.25 9.9 2.50781 9.23438 3.02344 8.69062C3.53906 8.14687 4.19062 7.875 4.97812 7.875C5.59687 7.875 6.15469 8.04844 6.65156 8.39531C7.14844 8.74219 7.5 9.1875 7.70625 9.73125C8.325 9.16875 9.03281 8.71875 9.82969 8.38125C10.6266 8.04375 11.475 7.875 12.375 7.875H24.75V12.375H19.125V14.625C19.125 16.5 18.4688 18.0938 17.1562 19.4062C15.8438 20.7188 14.25 21.375 12.375 21.375ZM5.0625 11.8125C5.38125 11.8125 5.64844 11.7047 5.86406 11.4891C6.07969 11.2734 6.1875 11.0062 6.1875 10.6875C6.1875 10.3687 6.07969 10.1016 5.86406 9.88594C5.64844 9.67031 5.38125 9.5625 5.0625 9.5625C4.74375 9.5625 4.47656 9.67031 4.26094 9.88594C4.04531 10.1016 3.9375 10.3687 3.9375 10.6875C3.9375 11.0062 4.04531 11.2734 4.26094 11.4891C4.47656 11.7047 4.74375 11.8125 5.0625 11.8125ZM12.375 18.5625C13.4625 18.5625 14.3906 18.1781 15.1594 17.4094C15.9281 16.6406 16.3125 15.7125 16.3125 14.625C16.3125 13.5375 15.9281 12.6094 15.1594 11.8406C14.3906 11.0719 13.4625 10.6875 12.375 10.6875C11.2875 10.6875 10.3594 11.0719 9.59062 11.8406C8.82187 12.6094 8.4375 13.5375 8.4375 14.625C8.4375 15.7125 8.82187 16.6406 9.59062 17.4094C10.3594 18.1781 11.2875 18.5625 12.375 18.5625Z" fill={color}/></Svg>);
const AssistantIcon = ({ color }: { color: string }) => ( <Svg height="32" width="32" viewBox="0 0 24 24"><Path d="M9 6H11V4H9V6ZM13 6V4H15V6H13ZM9 14V12H11V14H9ZM17 10V8H19V10H17ZM17 14V12H19V14H17ZM13 14V12H15V14H13ZM17 6V4H19V6H17ZM11 8V6H13V8H11ZM5 20V4H7V6H9V8H7V10H9V12H7V20H5ZM15 12V10H17V12H15ZM11 12V10H13V12H11ZM9 10V8H11V10H9ZM13 10V8H15V10H13ZM15 8V6H17V8H15Z" fill={color}/></Svg>);
const FourthOfficialIcon = ({ color }: { color: string }) => (
  <Svg width="24" height="24" viewBox="0 0 24 24">
    <Rect x="2" y="2" width="20" height="20" rx="4" stroke={color} strokeWidth="1.5" fill="none" />
    <SvgText x="12" y="16" textAnchor="middle" fontSize="12" fontWeight="600" fill={color}>
      4th
    </SvgText>
  </Svg>
);

// --- Reusable Components (Now accept styles and theme props) ---
const StatsCard = ({ title, value1, label1, value2, label2, styles, theme }: { title: string; value1: string; label1: string; value2: string; label2: string; styles: any; theme: any; }) => (
    <View style={styles.card}>
        <View style={styles.centeredHeaderContainer}>
            <Text style={styles.title}>{title}</Text>
        </View>
        <View style={styles.statsRow}>
            <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.primary }]}>{value1}</Text>
                <Text style={styles.statLabel}>{label1}</Text>
            </View>
            <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.primary }]}>{value2}</Text>
                <Text style={styles.statLabel}>{label2}</Text>
            </View>
        </View>
    </View>
);

const RoleDistributionCard = ({ styles, theme }: { styles: any; theme: any; }) => {
    const RoleIcons: Record<IconType, React.JSX.Element> = {
        referee: <RefereeIcon color={theme.primary} />,
        assistant: <AssistantIcon color={theme.primary} />,
        fourthOfficial: <FourthOfficialIcon color={theme.primary} />,
    };

    const barData: Array<{ value: number; icon: IconType; color: string }> = [
        { value: 66, icon: 'referee', color: theme.primary },
        { value: 24, icon: 'assistant', color: theme.primary },
        { value: 69, icon: 'fourthOfficial', color: theme.primary },
    ];
    const BAR_CHART_HEIGHT = 110;
    const maxValue = Math.max(...barData.map((d) => d.value), 1);
    const BAR_CHART_BAR_WIDTH = 25;

    return (
        <View style={styles.card}>
            <View style={styles.centeredHeaderContainer}>
                <Text style={styles.subtitle}>Number of Matches by role</Text>
            </View>
            <View style={styles.chartContainer}>
                {barData.map((item, index) => {
                    const cornerRadius = 8;
                    const barWidth = BAR_CHART_BAR_WIDTH;
                    const barHeight = (item.value / maxValue) * BAR_CHART_HEIGHT;
                    const effectiveBarHeight = Math.max(barHeight, cornerRadius * 2);
                    const yStart = BAR_CHART_HEIGHT - effectiveBarHeight;
                    const pathData = [`M ${-barWidth / 2}, ${BAR_CHART_HEIGHT}`,`L ${-barWidth / 2}, ${yStart + cornerRadius}`,`A ${cornerRadius},${cornerRadius} 0 0 1 ${ -barWidth / 2 + cornerRadius },${yStart}`,`L ${barWidth / 2 - cornerRadius}, ${yStart}`,`A ${cornerRadius},${cornerRadius} 0 0 1 ${barWidth / 2},${ yStart + cornerRadius }`,`L ${barWidth / 2}, ${BAR_CHART_HEIGHT}`,'Z'].join(' ');
                    return (
                        <View key={index} style={[styles.barWrapper, { flex: 1 }]}>
                            <View style={[styles.valueBox, { backgroundColor: item.color }]}><Text style={styles.valueText}>{item.value}</Text></View>
                            <Svg height={BAR_CHART_HEIGHT} width={styles.barWrapper.width}><Path d={pathData} fill={item.color} transform={`translate(${styles.barWrapper.width / 2}, 0)`}/></Svg>
                            <View style={styles.iconContainer}>{RoleIcons[item.icon]}</View>
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

const TeamGoalsCard = ({ styles, theme }: { styles: any; theme: any; }) => {
    const homeGoals = 185;
    const awayGoals = 157;
    return (
        <View style={styles.card}>
            <View style={styles.centeredHeaderContainer}>
                <Text style={styles.title}>Goals by team</Text>
            </View>
            <View style={styles.statsRow}>
                <View style={styles.statItem}><Text style={[styles.statValue, { color: theme.primary }]}>{homeGoals}</Text><Text style={styles.statLabel}>Home Goals</Text></View>
                <View style={styles.statItem}><Text style={[styles.statValue, { color: theme.primary }]}>{awayGoals}</Text><Text style={styles.statLabel}>Away Goals</Text></View>
            </View>
            <View style={styles.progressBarContainer}>
                <View style={[styles.homeBar, { flex: homeGoals }]} />
                <View style={[styles.awayBar, { flex: awayGoals }]} />
            </View>
        </View>
    );
};

const MatchesListCard = ({ title, matches, styles, theme }: { title: string, matches: MatchData[], styles: any, theme: any; }) => {
    return (
        <View style={styles.card}>
            <View style={styles.centeredHeaderContainer}>
                <Text style={styles.title}>{title}</Text>
            </View>
            {matches.map((match, index) => {
                const teams = match.teams.split(/ vs /i); // Split case-insensitively
                const homeTeam = teams[0];
                const awayTeam = teams.length > 1 ? teams[1] : '';

                return (
                    <View key={index} style={styles.matchRow}>
                        <View style={styles.matchScores}><Text style={[styles.scoreText, { color: theme.primary }]}>{`${match.score1}-${match.score2}`}</Text></View>
                        <View style={styles.matchDetails}>
                            <Text style={styles.teamNames}>
                                {homeTeam}
                                <Text style={{ color: theme.primary }}> vs </Text>
                                {awayTeam}
                            </Text>
                        </View>
                        <View style={styles.matchDateContainer}><Text style={styles.dateText}>{match.date}</Text></View>
                    </View>
                );
            })}
        </View>
    );
};


// --- Main Export Component ---
const Matches = () => {
    const { theme, isDarkMode } = useTheme();
    const styles = getStyles(theme, isDarkMode);

    const matchesData: MatchData[] = [
        { score1: 1, score2: 2, teams: 'Underwood Reserves fc vs Caldicot Castle FC Reserves', date: '09/09/2023' },
        { score1: 1, score2: 1, teams: 'Chepstow Town FC 3rd team vs Caldicot Castle FC Reserves', date: '09/02/2023' },
        { score1: 1, score2: 0, teams: 'Pontypool Town FC vs Albion Rovers FC 1st Team', date: '08/14/2024' },
    ];

    return (
        <ScrollView style={styles.screenContainer} showsVerticalScrollIndicator={false}>
            <StatsCard title="Total Matches" value1="74" label1="Matches" value2="3.50" label2="Avg per Season" styles={styles} theme={theme} />
            <RoleDistributionCard styles={styles} theme={theme} />
            <StatsCard title="Goals" value1="342" label1="Total" value2="4.56" label2="Avg Per Game" styles={styles} theme={theme} />
            <TeamGoalsCard styles={styles} theme={theme} />
            <MatchesListCard title="Matches with the most goals" matches={matchesData} styles={styles} theme={theme} />
        </ScrollView>
    );
};

// --- Dynamic Stylesheet ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: theme.background,
  },
  card: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 16,
    marginTop: 16,
    borderWidth: 1, // Always have a border for definition
    borderColor: theme.border,
    // Add shadow only for light mode
    shadowColor: isDarkMode ? 'transparent' : '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 1,
    elevation: isDarkMode ? 0 : 4,
  },
  centeredHeaderContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  title: { fontSize: 16, fontWeight: 'bold', color: theme.text },
  subtitle: { fontSize: 15, fontWeight: 'bold', color: theme.text, marginTop: 4 },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  statItem: {
      alignItems: 'center',
      width: '50%'
  },
  statValue: {
    fontSize: SCREEN_WIDTH < 375 ? 30 : 34,
    fontWeight: 'bold',
    lineHeight: SCREEN_WIDTH < 375 ? 36 : 40,
  },
  statLabel: { fontSize: 13, color: theme.text, marginTop: 4 },
  chartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: 180,
    marginTop: 10,
  },
  barWrapper: { alignItems: 'center', width: 45, marginHorizontal: 15 },
  valueBox: {
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginBottom: 8,
    maxWidth: '100%',
  },
  valueText: { color: 'white', fontSize: 14, fontWeight: 'bold' },
  iconContainer: {
    height: 35,
    width: 35,
    marginTop: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressBarContainer: {
    flexDirection: 'row',
    height: 12,
    borderRadius: 6,
    overflow: 'hidden',
    marginTop: 16,
  },
  homeBar: { backgroundColor: AppColors.Green },
  awayBar: { backgroundColor: AppColors.Orange },
  // --- ALIGNMENT FIX ---
  matchRow: { 
    flexDirection: 'row', 
    alignItems: 'flex-start', // Align all items to the top of the row
    paddingVertical: 15,
    borderBottomWidth: 1, 
    borderBottomColor: theme.border, 
  },
  matchScores: {
    width: 50,
    marginRight: 12,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    lineHeight: 20, // Match the line height of team names for better alignment
  },
  matchDetails: { 
    flex: 1,
  },
  teamNames: { 
    fontSize: 14, 
    color: theme.text, 
    lineHeight: 20, // Consistent line height
  },
  matchDateContainer: { 
    marginLeft: 16,
  },
  dateText: { 
    fontSize: 14, 
    color: theme.text, 
    opacity: 0.7, 
    lineHeight: 20, // Match the line height for alignment
  },
});

export default Matches;