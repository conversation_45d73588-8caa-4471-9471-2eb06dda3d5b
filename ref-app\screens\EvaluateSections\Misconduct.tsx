import React from 'react';
import { View, Text, StyleSheet, Dimensions, ScrollView } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext'; // Ensure this path is correct

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// --- Colors that don't change with theme ---
const AppColors = {
  pitchLightGreen: '#74b729',
  pitchDarkGreen: '#5e9422',
  pitchLineColor: 'rgba(255, 255, 255, 0.9)',
  pitchNumberColor: '#D4E93C',
  pitchNumberShadow: 'rgba(0, 0, 0, 0.4)',
  barYellow: '#FFD60A',
  barRed: '#E53935',
};

// --- Type Definitions ---
type MatchData = {
    score1: number;
    score2: number;
    teams: string;
    date: string;
};

interface CodeData {
    count: number;
    code: string;
}

// --- Reusable Components (Now accept styles and theme) ---
const StatsCard: React.FC<{ title: string; total: string; avg: string; styles: any; theme: any; }> = ({ title, total, avg, styles, theme }) => (
    <View style={styles.card}>
        <View style={styles.centeredHeaderContainer}>
            <Text style={styles.trendsSectionTitle}>{title}</Text>
        </View>
        <View style={styles.statsRow}>
            <View style={styles.statItem}>
                <Text style={[styles.trendsStatValue, { color: theme.primary }]}>{total}</Text>
                <Text style={styles.trendsStatLabel}>Total</Text>
            </View>
            <View style={styles.statItem}>
                <Text style={[styles.trendsStatValue, { color: theme.primary }]}>{avg}</Text>
                <Text style={styles.trendsStatLabel}>Avg Per Game</Text>
            </View>
        </View>
    </View>
);

const MatchesListCard: React.FC<{ matches: MatchData[]; styles: any; theme: any; }> = ({ matches, styles, theme }) => {
    return (
        <View style={styles.card}>
            <View style={styles.centeredHeaderContainer}>
                <Text style={styles.trendsSectionSubtitle}>Matches with the most incidents</Text>
            </View>
            {matches.map((match, index) => {
                const teams = match.teams.split(/ vs /i); // Split case-insensitively
                const homeTeam = teams[0];
                const awayTeam = teams.length > 1 ? teams[1] : '';

                return (
                    <View key={`match-list-${index}`} style={styles.matchRow}>
                        <View style={styles.matchScores}><Text style={[styles.scoreText, { color: theme.primary }]}>{`${match.score1}-${match.score2}`}</Text></View>
                        <View style={styles.matchDetails}>
                            <Text style={styles.teamNames}>
                                {homeTeam}
                                <Text style={{ color: theme.primary }}> vs </Text>
                                {awayTeam}
                            </Text>
                        </View>
                        <View style={styles.matchDateContainer}><Text style={styles.dateText}>{match.date}</Text></View>
                    </View>
                );
            })}
        </View>
    );
};

const PitchPositionsCard: React.FC<{ title: string; data: number[]; styles: any; }> = ({ title, data, styles }) => {
    const cellData = data || Array(18).fill(0);
    return (
        <View style={styles.card}>
            <View style={styles.centeredHeaderContainer}><Text style={styles.trendsSectionTitle}>{title}</Text></View>
            <View style={styles.pitchContainer}>
                <View style={styles.pitchStripeContainer}>
                  <View style={[styles.pitchStripe, { backgroundColor: AppColors.pitchLightGreen }]} />
                  <View style={[styles.pitchStripe, { backgroundColor: AppColors.pitchDarkGreen }]} />
                  <View style={[styles.pitchStripe, { backgroundColor: AppColors.pitchLightGreen }]} />
                  <View style={[styles.pitchStripe, { backgroundColor: AppColors.pitchDarkGreen }]} />
                  <View style={[styles.pitchStripe, { backgroundColor: AppColors.pitchLightGreen }]} />
                </View>
                <View style={[styles.pitchLine, styles.centerLine]} />
                <View style={[styles.pitchLine, styles.centerCircle]} />
                <View style={[styles.pitchLine, styles.penaltyBox, { top: 0 }]} />
                <View style={[styles.pitchLine, styles.goalArea, { top: 0 }]} />
                <View style={[styles.pitchLine, styles.penaltyArc, { top: '16.5%', transform: [{ rotate: '180deg' }] }]} />
                <View style={[styles.pitchLine, styles.penaltyBox, { bottom: 0 }]} />
                <View style={[styles.pitchLine, styles.goalArea, { bottom: 0 }]} />
                <View style={[styles.pitchLine, styles.penaltyArc, { bottom: '16.5%' }]} />
                {cellData.map((value, index) => {
                    const row = Math.floor(index / 3);
                    const col = index % 3;
                    return (<View key={index} style={[styles.pitchPosition, { left: `${col * 33.33}%`, top: `${row * 16.66}%` }]} ><Text style={styles.pitchNumber}>{value}</Text></View>);
                })}
            </View>
        </View>
    );
};

const CodeBarRow: React.FC<{item: CodeData; maxValue: number; barColor: string; styles: any;}> = ({ item, maxValue, barColor, styles }) => {
    const barWidth = maxValue > 0 ? (item.count / maxValue) * 100 : 0;
    return (
        <View style={styles.codeRow}>
            <Text style={styles.codeCount}>{item.count}</Text>
            <View style={styles.barWrapper}><View style={[styles.bar, { width: `${barWidth}%`, backgroundColor: barColor }]} /></View>
            <Text style={styles.codeLabel}>{item.code}</Text>
        </View>
    );
};

const CodesCard: React.FC<{ title: string; data: CodeData[]; barColor: string; styles: any; }> = ({ title, data, barColor, styles }) => {
    const maxValue = Math.max(...data.map(d => d.count), 0);
    return (
        <View style={styles.card}>
            <View style={styles.centeredHeaderContainer}>
                <Text style={styles.trendsSectionTitle}>{title}</Text>
            </View>
            {data.map((item, index) => (
                <CodeBarRow key={index} item={item} maxValue={maxValue} barColor={barColor} styles={styles} />
            ))}
        </View>
    );
};

// --- Main Export Component ---
const Misconduct = () => {
    const { theme, isDarkMode } = useTheme();
    const styles = getStyles(theme, isDarkMode);

    const yellowCardMatches: MatchData[] = [
        { score1: 1, score2: 2, teams: 'Underwood Reserves fc vs Caldicot Castle FC Reserves', date: '09/09/2023' },
        { score1: 1, score2: 1, teams: 'Chepstow Town FC 3rd team vs Caldicot Castle FC Reserves', date: '09/02/2023' },
        { score1: 1, score2: 0, teams: 'Pontypool Town FC vs Albion Rovers FC 1st Team', date: '08/14/2024' },
    ];
    const tempDismissalMatches: MatchData[] = [
        { score1: 3, score2: 1, teams: 'Newport County v Swansea City', date: '10/11/2024' },
        { score1: 0, score2: 0, teams: 'Cardiff Met FC v The New Saints', date: '10/05/2024' },
    ];
    const redCardMatches: MatchData[] = [
        { score1: 4, score2: 2, teams: 'Wrexham AFC v Notts County', date: '11/20/2024' },
    ];

    const yellowCardPositionData = [0, 1, 0, 1, 2, 1, 5, 8, 4, 18, 10, 18, 2, 21, 5, 0, 3, 1];
    const redCardPositionData = [0, 0, 1, 2, 0, 1, 4, 7, 2, 5, 0, 0, 1, 0, 0, 0, 0, 0];
    const yellowCardCodesData: CodeData[] = [
        { count: 93, code: 'C1' }, { count: 27, code: 'C2' },
        { count: 24, code: 'AA' }, { count: 15, code: 'C1' },
    ];
    const redCardCodesData: CodeData[] = [
        { count: 13, code: 'R7' }, { count: 4, code: 'S7' },
        { count: 3, code: 'S4' }, { count: 2, code: 'R6' },
    ];

    return (
        <ScrollView style={styles.screenContainer} showsVerticalScrollIndicator={false}>
            <StatsCard title="Yellow Cards" total="342" avg="4.56" styles={styles} theme={theme} />
            <MatchesListCard matches={yellowCardMatches} styles={styles} theme={theme} />
            <StatsCard title="Temporary Dismissals" total="150" avg="2.10" styles={styles} theme={theme} />
            <MatchesListCard matches={tempDismissalMatches} styles={styles} theme={theme} />
            <StatsCard title="Red Cards" total="890" avg="12.3" styles={styles} theme={theme} />
            <MatchesListCard matches={redCardMatches} styles={styles} theme={theme} />
            <PitchPositionsCard title="Yellow Card Positions" data={yellowCardPositionData} styles={styles} />
            <PitchPositionsCard title="Red Card Positions" data={redCardPositionData} styles={styles} />
            <CodesCard title="Yellow Card Codes" data={yellowCardCodesData} barColor={AppColors.barYellow} styles={styles} />
            <CodesCard title="Red Card Codes" data={redCardCodesData} barColor={AppColors.barRed} styles={styles} />
        </ScrollView>
    );
};

// --- Dynamic Styles ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
    screenContainer: { flex: 1, backgroundColor: theme.background },
    card: {
        backgroundColor: theme.card,
        borderRadius: 12,
        padding: 20,
        marginHorizontal: 16,
        marginVertical: 10,
        borderWidth: 1,
        borderColor: theme.border,
        shadowColor: isDarkMode ? 'transparent' : '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: isDarkMode ? 0 : 0.1,
        shadowRadius: 2,
        elevation: isDarkMode ? 0 : 3,
    },
    centeredHeaderContainer: { alignItems: 'center', marginBottom: 16, },
    trendsSectionTitle: { fontSize: 15, fontWeight: '600', color: theme.text },
    trendsSectionSubtitle: { fontSize: 14, fontWeight: '500', color: theme.text, opacity: 0.7 },
    statsRow: { flexDirection: 'row', justifyContent: 'center', },
    statItem: { alignItems: 'center', width: '50%' },
    trendsStatValue: { fontSize: 28, fontWeight: 'bold', color: theme.primary },
    trendsStatLabel: { fontSize: 13, color: theme.text, opacity: 0.7, marginTop: 4 },
    matchRow: { flexDirection: 'row', alignItems: 'flex-start', paddingBottom: 15, marginBottom: 15, borderBottomWidth: 1, borderBottomColor: theme.border },
    matchScores: { width: 50, marginRight: 12, },
    scoreText: { fontSize: 16, fontWeight: 'bold', lineHeight: 20 },
    matchDetails: { flex: 1 },
    teamNames: { fontSize: 14, color: theme.text, lineHeight: 20 },
    matchDateContainer: { alignItems: 'flex-end', marginLeft: 8, },
    dateText: { fontSize: 14, color: theme.text, opacity: 0.7, lineHeight: 20 },
    pitchContainer: {
        width: '100%',
        aspectRatio: 0.65,
        borderWidth: 2,
        borderColor: AppColors.pitchLineColor,
        borderRadius: 8,
        overflow: 'hidden',
        position: 'relative',
        backgroundColor: AppColors.pitchLightGreen,
    },
    pitchStripeContainer: { position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, flexDirection: 'column', },
    pitchStripe: { flex: 1 },
    pitchPosition: { position: 'absolute', justifyContent: 'center', alignItems: 'center', width: '33.33%', height: '16.66%', },
    pitchNumber: {
        fontSize: SCREEN_WIDTH / 18,
        fontWeight: 'bold',
        color: AppColors.pitchNumberColor,
        textShadowColor: AppColors.pitchNumberShadow,
        textShadowOffset: { width: 1, height: 2 },
        textShadowRadius: 2,
    },
    pitchLine: { position: 'absolute', borderColor: AppColors.pitchLineColor, borderWidth: 1, },
    centerLine: { width: '100%', height: 2, top: '50%', marginTop: -1, backgroundColor: AppColors.pitchLineColor, borderWidth: 0, },
    centerCircle: { width: 60, height: 60, borderRadius: 30, position: 'absolute', top: '50%', left: '50%', marginLeft: -30, marginTop: -30, },
    penaltyBox: { width: '60%', height: '16.5%', left: '20%', borderBottomWidth: 2, borderLeftWidth: 2, borderRightWidth: 2, },
    goalArea: { width: '30%', height: '8%', left: '35%', borderBottomWidth: 2, borderLeftWidth: 2, borderRightWidth: 2, },
    penaltyArc: { width: 40, height: 20, borderRadius: 20, borderBottomWidth: 2, position: 'absolute', left: '50%', marginLeft: -20, marginBottom: -10, },
    codeRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 10, },
    codeCount: { width: 30, fontSize: 13, fontWeight: 'bold', color: theme.text, },
    barWrapper: { flex: 1, height: 24, marginHorizontal: 12, backgroundColor: theme.border, borderRadius: 8, },
    bar: { height: '100%', borderRadius: 8, },
    codeLabel: { width: 35, textAlign: 'right', fontSize: 13, fontWeight: 'bold', color: theme.text, opacity: 0.7 },
});

export default Misconduct;