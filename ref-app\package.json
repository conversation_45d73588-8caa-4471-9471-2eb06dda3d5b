{"name": "mobile-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.15", "@react-navigation/native": "^7.1.11", "@react-navigation/native-stack": "^7.3.16", "@react-navigation/stack": "^7.3.4", "@types/react-native-vector-icons": "^6.4.18", "axios": "^1.10.0", "expo": "53.0.13", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.2", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "firebase": "^11.9.1", "react": "19.0.0", "react-native": "0.79.4", "react-native-chart-kit": "^6.12.0", "react-native-circular-progress": "^1.4.1", "react-native-fbsdk-next": "^13.4.1", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.61", "react-native-linear-gradient": "^2.8.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-size-matters": "^0.4.2", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/react": "~19.0.10", "eas-cli": "^16.12.0", "typescript": "~5.8.3"}, "private": true}