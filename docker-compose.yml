services:
  postgres:
    image: postgres:16.9
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"



  redis:
    image: redis:7.2.6
    ports:
      - "6379:6379"

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: minio123
    ports:
      - "9000:9000"   # S3 API
      - "9001:9001"   # Web console

  api:
    build:
      context: ./ref-api
      dockerfile: Dockerfile
    ports:
      - "0.0.0.0:3000:3000"
    env_file:
      - ./ref-api/.env
    environment:
      DATABASE_URL: "******************************************/postgres?schema=public"
    volumes:
      - ./ref-api/src:/app/src
      - ./ref-api/prisma:/app/prisma
    depends_on:
      - postgres
      - redis
      - minio

volumes:
  pgdata: